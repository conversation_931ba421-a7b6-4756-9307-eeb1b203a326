import path from "path";
import fs from 'fs';
import util from 'util';
import { CrumbOutput, getCrumbFileName } from "../src/server/routes/client-files";

/**
 * 
 * @param loadBaseCrumbs 
 * @param getCrumbsOutput 
 * @param applyChanges 
 * @param createCrumbs 
 * @param crumbsPath 
 */
export async function generateCrumbFiles<CrumbPatch>(
  loadBaseCrumbs: () => Promise<string>,
  getCrumbsOutput: () => CrumbOutput<CrumbPatch>,
  applyChanges: (content: string, changes: Partial<CrumbPatch>) =>string,
  createCrumbs: (path: string, file: string) => Promise<void>,
  crumbsPath: string
): Promise<void> {
  const autoGeneratedDir = path.join(__dirname, '..', 'media', crumbsPath);
  // cache will be used to prevent wasting work by recompiling stuff in FFDEC when the file is already here
  const CRUMB_CACHE = path.join(autoGeneratedDir, 'crumbcache.json');
  // when building the media for production, the cache file shouldn't be present
  const PRODUCTION_BUILD = process.env.PRODUCTION_BUILD !== undefined;

  const baseCrumbs = await loadBaseCrumbs();
  const { hash, crumbs } = getCrumbsOutput();

  // map of each output ID (a unique ID from crumbs output)
  // and their respective content (each content is meant to be unique)
  const crumbContents = new Map<number, string>();

  crumbs.forEach((crumb, i) => {
    if (!crumbContents.has(crumb.id)) {
      console.log(`Patching crumb ${i}/${crumbs.length}`);
      crumbContents.set(crumb.id, applyChanges(baseCrumbs, crumb.out));
    }
  });
  if (!fs.existsSync(autoGeneratedDir)) {
    fs.mkdirSync(autoGeneratedDir, { recursive: true });
  }

  let promises: Promise<any>[] = [];

  let previousCache: Record<string, string> = {};
  if (fs.existsSync(CRUMB_CACHE) && !PRODUCTION_BUILD) {
    previousCache = JSON.parse(fs.readFileSync(CRUMB_CACHE, { encoding: 'utf-8' }));
  }

  // cache of current session, to save for the future
  const crumbCache: Record<string, string> = {};

  let i = 0;
  const entries = Array.from(crumbContents.entries());
  for (const [crumbId, crumbContent] of entries) {
    console.log(`Exporting file ${i}/${entries.length}`);
    i++;
    if (promises.length > 16) {
      await Promise.all(promises);
      promises = [];
    }
    const previousName = previousCache[crumbContent];
    const fileName = getCrumbFileName(hash, crumbId);
    const newFilePath = path.join(autoGeneratedDir, fileName);
    crumbCache[crumbContent] = fileName;
    if (previousName === undefined) {
      promises.push(createCrumbs(newFilePath, crumbContent));
    } else {
      // in case running when no chages have been made        
      if (previousName !== fileName) {
        // just move the file
        // an error here can be because an object that has a change coincides with base crumbs
        promises.push(util.promisify(fs.rename)(path.join(autoGeneratedDir, previousName), newFilePath));
      }
    }
  }
  await Promise.all(promises);

  // deleting unwanted past files
  fs.readdirSync(autoGeneratedDir).forEach((file) => {
    if (!file.startsWith(hash) && !CRUMB_CACHE.includes(file)) {
      fs.unlinkSync(path.join(autoGeneratedDir, file));
    }
  });

  if (PRODUCTION_BUILD) {
    if (fs.existsSync(CRUMB_CACHE)) {
      fs.unlinkSync(CRUMB_CACHE);
    }
  } else {
    fs.writeFileSync(CRUMB_CACHE, JSON.stringify(crumbCache))
  }
}