import { RoomName } from "./rooms";

export const ORIGINAL_ROOMS: Partial<Record<RoomName, string>> = {
  'town': 'approximation:town_release.swf',
  'coffee': 'fix:ArtworkRoomsCoffee2.swf',
  'book': 'archives:Beta-book.swf',
  'dance': 'mammoth:artwork/rooms/dance10.swf',
  'lounge': 'mammoth:artwork/rooms/lounge10.swf',
  'shop': 'mammoth:artwork/rooms/shop10.swf',
  'dock': 'mammoth:artwork/rooms/dock11.swf',
  'village': 'approximation:village_release.swf',
  'rink': 'approximation:rink_release.swf',
  'dojo': 'mammoth:artwork/rooms/dojo10.swf',
  'agent': 'archives:ArtworkRoomsAgent10.swf' // HQ is disputed, maybe not original release
}
