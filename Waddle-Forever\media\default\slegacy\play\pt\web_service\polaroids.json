{"7": {"name": "Activities", "description": "Doing things in-game", "display": "Activities", "polaroids": [{"polaroid_id": "1", "stamp_count": "1", "description": "Activities_A"}, {"polaroid_id": "2", "stamp_count": "9", "description": "Activities_B"}, {"polaroid_id": "3", "stamp_count": "17", "description": "Activities_C"}]}, "6": {"name": "Characters", "description": "Characters", "display": "Events : Characters", "polaroids": [{"polaroid_id": "10", "stamp_count": "1", "description": "Characters_A"}, {"polaroid_id": "11", "stamp_count": "4", "description": "Characters_B"}, {"polaroid_id": "12", "stamp_count": "7", "description": "Characters_C"}]}, "23": {"name": "Party", "description": "Party Stamps", "display": "Events : Party", "polaroids": [{"polaroid_id": "30", "stamp_count": "1", "description": "Party at the Stadium"}, {"polaroid_id": "31", "stamp_count": "2", "description": "4 penguins celebrating"}, {"polaroid_id": "52", "stamp_count": "12", "description": "coinsforchange_1"}]}, "13": {"name": "Aqua Grabber", "description": "Aqua Grabber related stamps", "display": "Games : <PERSON><PERSON>", "polaroids": [{"polaroid_id": "4", "stamp_count": "1", "description": "aquagrabber_A"}, {"polaroid_id": "5", "stamp_count": "10", "description": "aquagrabber_B"}, {"polaroid_id": "6", "stamp_count": "20", "description": "aquagrabber_C"}]}, "14": {"name": "Astro Barrier", "description": "Astro Barrier related stamps", "display": "Games : <PERSON><PERSON>", "polaroids": [{"polaroid_id": "7", "stamp_count": "1", "description": "astrobarrier_A"}, {"polaroid_id": "8", "stamp_count": "6", "description": "astrobarrier_B"}, {"polaroid_id": "9", "stamp_count": "12", "description": "astrobarrier_C"}]}, "38": {"name": "Card-Jitsu", "description": "Card-Jitsu Original", "display": "Games : Card-Jitsu", "polaroids": [{"polaroid_id": "42", "stamp_count": "1", "description": "Card_Jitsu_Basic_A"}, {"polaroid_id": "43", "stamp_count": "5", "description": "Card_Jitsu_Basic_B"}, {"polaroid_id": "44", "stamp_count": "10", "description": "Card_Jitsu_Basic_C"}]}, "32": {"name": "Card-Jitsu : Fire", "description": "Card-Jitsu Fire", "display": "Games : Card-Jitsu : Fire", "polaroids": [{"polaroid_id": "45", "stamp_count": "1", "description": "Card_Jitsu_Fire_A"}, {"polaroid_id": "46", "stamp_count": "4", "description": "Card_Jitsu_Fire_B"}, {"polaroid_id": "47", "stamp_count": "8", "description": "Card_Jitsu_Fire_C"}]}, "34": {"name": "Card-Jitsu : Water", "description": "Card-Jitsu Water", "display": "Games : Card-Jitsu : Water", "polaroids": [{"polaroid_id": "48", "stamp_count": "1", "description": "Card_Jitsu_Water_A"}, {"polaroid_id": "49", "stamp_count": "4", "description": "Card_Jitsu_Water_B"}, {"polaroid_id": "50", "stamp_count": "8", "description": "Card_Jitsu_Water_C"}]}, "28": {"name": "<PERSON><PERSON>", "description": "<PERSON>t <PERSON>fer Stamps", "display": "Games : <PERSON><PERSON>", "polaroids": [{"polaroid_id": "38", "stamp_count": "1", "description": "Penguin standing on cart"}, {"polaroid_id": "39", "stamp_count": "6", "description": "Penguin Holding Black Puffle"}, {"polaroid_id": "40", "stamp_count": "12", "description": "penguin doing handstand on cart"}]}, "15": {"name": "Catchin' Waves", "description": "Stamps related to surfing", "display": "Games : Catchin' Waves", "polaroids": [{"polaroid_id": "25", "stamp_count": "1", "description": "catchinwaves_A"}, {"polaroid_id": "26", "stamp_count": "10", "description": "catchinwaves_B"}, {"polaroid_id": "27", "stamp_count": "22", "description": "catchinwaves_C"}]}, "11": {"name": "Jet Pack Adventure", "description": "All stamps for flying", "display": "Games : Jet Pack Adventure", "polaroids": [{"polaroid_id": "13", "stamp_count": "1", "description": "jetpack_A"}, {"polaroid_id": "14", "stamp_count": "7", "description": "jetpack_B"}, {"polaroid_id": "15", "stamp_count": "14", "description": "jetpack_C"}, {"polaroid_id": "41", "stamp_count": "18", "description": "jetpack_D"}]}, "22": {"name": "Missions", "description": "PSA Missions 1 to 11", "display": "Games : Missions", "polaroids": [{"polaroid_id": "35", "stamp_count": "1", "description": "Agent proudly walking away from a happy Aunt <PERSON><PERSON>"}, {"polaroid_id": "36", "stamp_count": "11", "description": "Agent talking to <PERSON> and <PERSON>, <PERSON> n <PERSON> screen"}, {"polaroid_id": "37", "stamp_count": "22", "description": "Penguin and puffle swimming in foreground, Shockto"}]}, "19": {"name": "Puffle Rescue", "description": "Puffle Rescue Stamps", "display": "Games : <PERSON><PERSON>le Rescue", "polaroids": [{"polaroid_id": "33", "stamp_count": "1", "description": "<PERSON> jumping onto ice block, puffle in backgrou"}, {"polaroid_id": "32", "stamp_count": "15", "description": "<PERSON> riding mine cart, puffle on cliff in backg"}, {"polaroid_id": "34", "stamp_count": "27", "description": "Penguin and puffle swimming in foreground, Shockto"}]}, "46": {"name": "System Defender", "description": "System Defender", "display": "Games : System Defender", "polaroids": [{"polaroid_id": "53", "stamp_count": "1", "description": "systemdefender_1"}, {"polaroid_id": "54", "stamp_count": "5", "description": "systemdefender_2"}, {"polaroid_id": "55", "stamp_count": "16", "description": "systemdefender_3"}]}, "16": {"name": "Thin Ice", "description": "Don't fall through!", "display": "Games : Thin Ice", "polaroids": [{"polaroid_id": "22", "stamp_count": "1", "description": "thinice_A"}, {"polaroid_id": "23", "stamp_count": "5", "description": "thinice_B"}, {"polaroid_id": "24", "stamp_count": "9", "description": "thinice_C"}]}, "48": {"name": "Puffle Launch", "description": "Puffle Launch", "display": "Puffle Launch", "polaroids": [{"polaroid_id": "56", "stamp_count": "1", "description": "pufflelaunch_1"}, {"polaroid_id": "57", "stamp_count": "6", "description": "pufflelaunch_2"}, {"polaroid_id": "58", "stamp_count": "12", "description": "pufflelaunch_3"}]}, "26": {"name": "Game Day", "description": "Wii Game 'Game Day'", "display": "Video Games : Game Day", "polaroids": [{"polaroid_id": "28", "stamp_count": "1", "description": "Red Team Party"}, {"polaroid_id": "29", "stamp_count": "7", "description": "Java Sack Race"}, {"polaroid_id": "51", "stamp_count": "14", "description": "Sports Celebration"}]}}