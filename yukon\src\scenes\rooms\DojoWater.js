import RoomScene from '@scenes/rooms/RoomScene'

import { Animation, Button, MoveTo, SimpleButton, Zone } from '@components/components'


/* START OF COMPILED CODE */

export default class DojoWater extends RoomScene {

    constructor() {
        super("DojoWater");

        /** @type {Phaser.GameObjects.Image} */
        this.floor;
        /** @type {Array<Phaser.GameObjects.Image|Phaser.GameObjects.Sprite>} */
        this.sort;


        /* START-USER-CTR-CODE */
        this.roomTriggers = {
            'dojo': () => this.triggerRoom(320, 320, 240)
        }
        this.music = 'dojo'
        /* END-USER-CTR-CODE */
    }

    /** @returns {void} */
    _preload() {

        this.load.pack("dojowater-pack", "assets/media/rooms/dojowater/dojowater-pack.json");
    }

    /** @returns {void} */
    _create() {

        // floor
        const floor = this.add.image(760, 480, "dojowater", "bg");
        floor.setOrigin(0.5, 0.5);

        // water_mat
        const water_mat = this.add.image(760, 600, "dojowater", "water_mat");
        water_mat.setOrigin(0.5, 0.5);

        // sensei_mat
        const sensei_mat = this.add.image(760, 320, "dojowater", "sensei_mat");
        sensei_mat.setOrigin(0.5, 0.5);

        // door
        const door = this.add.image(200, 400, "dojowater", "door");
        door.setOrigin(0.5, 0.5);

        // door_zone
        const door_zone = this.add.rectangle(200, 400, 100, 200);
        door_zone.setOrigin(0.5, 0.5);
        door_zone.isFilled = true;
        door_zone.fillColor = 65280;
        door_zone.fillAlpha = 0.5;

        // card_jitsu_water_zone
        const card_jitsu_water_zone = this.add.rectangle(760, 600, 200, 100);
        card_jitsu_water_zone.setOrigin(0.5, 0.5);
        card_jitsu_water_zone.isFilled = true;
        card_jitsu_water_zone.fillColor = 255;
        card_jitsu_water_zone.fillAlpha = 0.5;

        // sensei_zone
        const sensei_zone = this.add.rectangle(760, 320, 150, 100);
        sensei_zone.setOrigin(0.5, 0.5);
        sensei_zone.isFilled = true;
        sensei_zone.fillColor = 16711680;
        sensei_zone.fillAlpha = 0.5;

        // lists
        const sort = [water_mat, sensei_mat, door];

        // door_zone (components)
        const door_zoneMoveTo = new MoveTo(door_zone);
        door_zoneMoveTo.x = 320;
        door_zoneMoveTo.y = 320;

        // card_jitsu_water_zone (components)
        const card_jitsu_water_zoneSimpleButton = new SimpleButton(card_jitsu_water_zone);
        card_jitsu_water_zoneSimpleButton.callback = () => this.onCardJitsuWater();

        // sensei_zone (components)
        const sensei_zoneSimpleButton = new SimpleButton(sensei_zone);
        sensei_zoneSimpleButton.callback = () => this.onSensei();

        this.floor = floor;
        this.sort = sort;

        this.events.emit("scene-awake");
    }


    /* START-USER-CODE */

    onCardJitsuWater() {
        this.interface.loadWidget('CardJitsuWater')
    }

    onSensei() {
        // Show sensei dialog for water dojo
        this.interface.prompt.showWindow('Talk to Sensei about the Water element?', 'dual', () => {
            this.interface.loadWidget('CardJitsuWater')
        })
    }

    /* END-USER-CODE */
}

/* END OF COMPILED CODE */
