import RoomScene from "./RoomScene"
import Button from "../components/Button"
import MoveTo from "../components/MoveTo"
import SimpleButton from "../components/SimpleButton"


/* START OF COMPILED CODE */

export default class DojoHide extends RoomScene {

    constructor() {
        super("DojoHide");

        /** @type {Phaser.GameObjects.Image} */
        this.floor;
        /** @type {Array<Phaser.GameObjects.Image|Phaser.GameObjects.Sprite>} */
        this.sort;


        /* START-USER-CTR-CODE */
        this.roomTriggers = {
            'dojo': () => this.triggerRoom(320, 320, 240)
        }
        this.music = 'dojo'
        /* END-USER-CTR-CODE */
    }

    /** @returns {void} */
    _preload() {

        this.load.pack("dojohide-pack", "assets/media/rooms/dojohide/dojohide-pack.json");
    }

    /** @returns {void} */
    _create() {

        // floor
        const floor = this.add.image(760, 480, "dojohide", "bg");
        floor.setOrigin(0.5, 0.5);

        // fire_door
        const fire_door = this.add.image(400, 300, "dojohide", "fire_door");
        fire_door.setOrigin(0.5, 0.5);

        // water_door
        const water_door = this.add.image(1120, 300, "dojohide", "water_door");
        water_door.setOrigin(0.5, 0.5);

        // exit_door
        const exit_door = this.add.image(760, 600, "dojohide", "exit_door");
        exit_door.setOrigin(0.5, 0.5);

        // fire_door_zone
        const fire_door_zone = this.add.rectangle(400, 300, 100, 150);
        fire_door_zone.setOrigin(0.5, 0.5);
        fire_door_zone.isFilled = true;
        fire_door_zone.fillColor = 16711680;
        fire_door_zone.fillAlpha = 0.5;

        // water_door_zone
        const water_door_zone = this.add.rectangle(1120, 300, 100, 150);
        water_door_zone.setOrigin(0.5, 0.5);
        water_door_zone.isFilled = true;
        water_door_zone.fillColor = 255;
        water_door_zone.fillAlpha = 0.5;

        // exit_door_zone
        const exit_door_zone = this.add.rectangle(760, 600, 150, 100);
        exit_door_zone.setOrigin(0.5, 0.5);
        exit_door_zone.isFilled = true;
        exit_door_zone.fillColor = 65280;
        exit_door_zone.fillAlpha = 0.5;

        // lists
        const sort = [fire_door, water_door, exit_door];

        // fire_door_zone (components)
        const fire_door_zoneSimpleButton = new SimpleButton(fire_door_zone);
        fire_door_zoneSimpleButton.callback = () => this.triggerRoom(321, 760, 400);

        // water_door_zone (components)
        const water_door_zoneSimpleButton = new SimpleButton(water_door_zone);
        water_door_zoneSimpleButton.callback = () => this.triggerRoom(322, 760, 400);

        // exit_door_zone (components)
        const exit_door_zoneMoveTo = new MoveTo(exit_door_zone);
        exit_door_zoneMoveTo.x = 320;
        exit_door_zoneMoveTo.y = 320;

        this.floor = floor;
        this.sort = sort;

        this.events.emit("scene-awake");
    }


    /* START-USER-CODE */

    // This room serves as the hub to access Fire and Water dojos

    /* END-USER-CODE */
}

/* END OF COMPILED CODE */
