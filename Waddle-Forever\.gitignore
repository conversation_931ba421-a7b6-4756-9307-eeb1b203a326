.DS_Store
.env
.gclient_done
**/.npmrc
.tags*
.vs/
.vscode/
*.log
*.pyc
*.sln
*.swp
*.VC.db
*.VC.VC.opendb
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.xcodeproj
/.idea/
/dist/
/compiled/
node_modules/
SHASUMS256.txt
**/package-lock.json
compile_commands.json
.envrc

# npm package
/npm/dist
/npm/path.txt
/npm/checksums.json

.npmrc

# Generated API definitions
electron-api.json
electron.d.ts

# Spec hash calculation
spec/.hash

# Eslint Cache
.eslintcache*

# Generated native addon files
/spec/fixtures/native-addon/echo/build/

# If someone runs tsc this is where stuff will end up
ts-gen

# Used to accelerate CI builds
.depshash
.depshash-target

# Used to accelerate builds after sync
patches/mtime-cache.json

spec/fixtures/logo.png

# Game database
/data/

# settings file is autogenerated
settings.json

# testing updater
/tempupdate

# zip files output
/zip

# crumbs output
/crumbs/out
/crumbs/news
/media/default/auto/

# mods folder
/mods

# FFDEC path depends on your local installation
ffdec-path.ts

# production logs
/logs

# file management
/labels/warnings.txt

# auto generated media info
/src/server/game-data/package-info.ts

# versions dump for debugging
versions-dump.txt