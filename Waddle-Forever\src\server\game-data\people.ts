import { StaticDataTable } from "../../common/static-table";

type Person = {
  id: number;
  name: string;
};

const PEOPLE = new StaticDataTable<Person, ['id', 'name']>(
  ['id', 'name'],
  [
    [1, 'supermanover'],
    [2, 'nhaar'],
    [3, '<PERSON><PERSON>'],
    [4, '<PERSON><PERSON><PERSON><PERSON><PERSON>'],
    [5, '<PERSON>'],
    [6, '<PERSON><PERSON>'],
    [7, '<PERSON><PERSON><PERSON><PERSON>'],
    [8, 'victando']
  ]
);