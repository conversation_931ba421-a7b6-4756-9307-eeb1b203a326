﻿textData({
"dynamic" : {
"D1" : "are now available!",
"D2" : "is now available!",
"D3" : "are online!",
"D4" : "is online!",
"D5" : "You have {0} friend requests!",
"D6" : "wants to be your friend!",
"D7" : "Undo Best Friend",
"D8" : "You are now friends with {0}",
"D9" : "Friends",
"D10" : "Make Best Friend",
"D11" : "Choose your best friends",
"D12" : "You have {0} new friends!",
"D14" : "Sorry, this room is full.",
"D15" : "Offline",
"D17" : "Sorry, we can't find that player. Try entering another name.",
"D18" : "Oops! You can't add yourself to your own friend list.",
"D19" : "{0} is already a friend. Try entering another name.",
"D20" : "{0} is already on your Ignore List. Go to settings to remove them from your Ignore List.",
"D21" : "new friends!",
"D22" : "Igloo",
"D23" : "Online",
"D24" : "Mobile",
"D25" : "is logged in!",
"D26" : "Your parent needs to activate your account before you can add friends.",
"D27" : "About Activation"
},
"S1" : {"h":"Friends"},
"S2" : {"t":"Close Panel"},
"S3" : {"h":"Find a Player"},
"S4" : {"t":"Search for a friend"}, 
"S5" : {"h":"Find"},
"S6" : {"t":"Reconnecting..."},
"S7" : {"t":"Show Player Card"},
"S8" : {"h":"+ Add Friend"},
"S9" : {"h":"No Thanks"},
"S10" : {"t":"Go to friend in game"},
"S11" : {"h":"Accepted"},
"S12" : {"a":"*","t":"Make Best Friend"},
"S14" : {"h":"Offline"},
"S15" : {"t":"Scroll up"},
"S16" : {"t":"Scroll down"},
"S17" : {"a":"Avatar"},
"S18" : {"t":"Drag and drop the panel"},
"S19" : {"t":"Show friends"},
"S20" : {"t":"Scroll up and down"},
"S21" : {"h":"Enter Name"},
"S22" : {"t":"Add Friend"}
});