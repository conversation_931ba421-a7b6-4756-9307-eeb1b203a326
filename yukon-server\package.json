{"name": "yukon-server", "version": "1.10.1-beta", "description": "A server for Yukon", "scripts": {"dev": "babel-watch ./src/World.js Login Blizzard", "build": "rimraf dist && babel src -d dist --copy-files", "start": "pm2 start ecosystem.config.js", "stop": "pm2 stop ecosystem.config.js", "restart": "pm2 restart ecosystem.config.js", "list": "pm2 list", "logs": "pm2 logs", "monit": "pm2 monit", "secret-gen": "node ./utils/secret-gen.js"}, "author": "wizguin", "license": "MIT", "devDependencies": {"@babel/cli": "^7.24.0", "@babel/core": "^7.24.0", "@babel/node": "^7.23.9", "@babel/preset-env": "^7.24.0", "babel-plugin-module-resolver": "^4.1.0", "babel-watch": "^7.0.0", "eslint": "^8.56.0", "rimraf": "^5.0.0"}, "dependencies": {"bcrypt": "^5.1.0", "fastest-validator": "^1.17.0", "nodemailer": "^6.9.8", "jsonwebtoken": "^9.0.2", "mysql2": "^3.9.0", "pm2": "^5.3.0", "rate-limiter-flexible": "^3.0.0", "sequelize": "^6.35.0", "socket.io": "^4.7.0", "uuid": "^9.0.0"}, "optionalDependencies": {"bufferutil": "^4.0.7", "utf-8-validate": "^6.0.4"}}