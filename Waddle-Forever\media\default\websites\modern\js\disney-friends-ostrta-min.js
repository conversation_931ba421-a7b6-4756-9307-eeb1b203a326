DISNEY_FRIENDS_INI={header:null,ieVer:-1,VERSION:"1.3.59",FRIENDS_BASE_URL:window.location.protocol + "//" + window.location.host + "/",DEBUG:false,scriptNo:-1,styles:[],stylesIE:[],stylesIE7:[],stylesClubIE7:[],stylesClub:[{href:"css/themes/club.css",media:"screen"}],stylesMinified:[{href:"js/lib/css/jquery.ui.css",media:"all"},{href:"css/disney-friends.css",media:"all"}],stylesIEMinified:[{href:"js/lib/css/jquery.ui.css",media:"all"},{href:"css/disney-friends-ie.css",media:"all"}],stylesIE7Minified:[{href:"js/lib/css/jquery.ui.css",
media:"all"},{href:"css/disney-friends-ie7.css",media:"all"}],themeClubPenguinMinified:[{href:"css/themes/disney-clubpenguin.css",media:"all"}],themeClubPenguinIE7Minified:[{href:"css/themes/disney-clubpenguin-ie7.css",media:"all"}],libraryScripts:["js/lib/jquery.min.js","js/lib/jquery.ui.min.js","js/lib/libs.min.js"],dceScripts:[],init:function(){this.setProperties();this.getIEVersion();this.scripts=this.libraryScripts.concat(this.dceScripts);this.scriptsMinified=this.libraryScripts.concat(["js/disney/disney-friends-with-ui-min.js"]);
this.add2DOM()},setProperties:function(){},add2DOM:function(){this.header=document.getElementsByTagName("head")[0];if(this.ieVer>0)if(this.ieVer<8){this.addStyleTags(this.stylesIE7Minified);this.addStyleTags(this.themeClubPenguinIE7Minified)}else this.addStyleTags(this.stylesIEMinified);else this.addStyleTags(this.stylesMinified);if(this.ieVer<0||this.ieVer>=8)this.addStyleTags(this.stylesClub);this.loadScript()},addStyleTags:function(a){var b,c,d;c=0;for(d=a.length;c<d;c++){b=this.makeLink(a[c]);
this.header.appendChild(b)}},loadScript:function(){if(DISNEY_FRIENDS_INI!==null){DISNEY_FRIENDS_INI.scriptNo++;var a=null;if(a=DISNEY_FRIENDS_INI.scriptsMinified[DISNEY_FRIENDS_INI.scriptNo]){a=DISNEY_FRIENDS_INI.makeScript(a);DISNEY_FRIENDS_INI.header.appendChild(a)}}},makeLink:function(a){var b=document.createElement("link");b.type="text/css";b.rel="stylesheet";b.href=this.FRIENDS_BASE_URL+a.href+"?v="+this.VERSION;b.media=a.media;return b},makeScript:function(a){var b=document.createElement("script");
b.type="text/javascript";b.charset="utf-8";b.onreadystatechange=function(){if(this.readyState==="loaded"||this.readyState==="complete")DISNEY_FRIENDS_INI.loadScript()};b.onload=this.loadScript;b.src=this.FRIENDS_BASE_URL+a+"?v="+this.VERSION;return b},makeDiv:function(a,b){var c=document.createElement("div");c.id=a;c.innerHTML=b;return c},getIEVersion:function(){var a=-1;if(navigator.appName=="Microsoft Internet Explorer")if(/MSIE ([0-9]{1,}[.0-9]{0,})/.exec(navigator.userAgent)!==null)a=parseFloat(RegExp.$1);
this.ieVer=a},getUrlVars:function(){var a=[],b,c,d=window.location.href.slice(window.location.href.indexOf("?")+1).split("&");for(c=0;c<d.length;c++){b=d[c].split("=");a.push(b[0]);a[b[0]]=b[1]}return a},getUrlVar:function(a){return this.getUrlVars()[a]}};DISNEY_FRIENDS_INI.init();
