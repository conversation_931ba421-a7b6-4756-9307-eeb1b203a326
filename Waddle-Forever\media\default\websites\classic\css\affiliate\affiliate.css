/* ______________________________________________________________
   __________________________ Affiliate Mockup _______________________ */
	
body{font-family: Arial, Helvetica, sans-serif; 
	margin:0; 
	padding:0; 
	text-align:center; 
	background: #fff; 
	height:100%; 
	width: 100%;
	font-size:small;
	min-width:990px;
	font-size: x-small; /* IE5 Win */
  	voice-family: "\"}\""; 
  	voice-family: inherit;
  	font-size: small;
	}
	
html>body { /* be nice to Opera */
	font-size: small;
	}
	
strong { font-weight:bold;}

.border { 
	background:url(/images/game-border.gif) no-repeat;
	width:784px;
	height:502px;
	margin: 0px auto;
	padding: 10px 0 0 0;
	text-align: center;
	}
	
/* Main Layout
________________________________________________________*/

#affiliateheader {
width: 100%;
margin: 0px auto 5px;
text-align: left;
}

#affiliatefooter {
margin: 5px auto 0px;
clear: both;
width: 100%;
text-align: left;
}

#my_flash {
clear: both;
}

/*Main Elements
_______________________________________________________*/
	
/* HTML Navigation */
	
#hdrWrap{/*This houses the main background for the home page Flash navigation*/
	position: relative;
	height:32px !important;
	width: 100%;
	margin: 0 0 -5px;
	background: url(/images/playNavBg.gif) 0 0 repeat-x;
	z-index:4;
	overflow: hidden;
}

#htmlNav{/*This houses the main HTML navigation*/
	width:842px;
	height:32px !important;
	margin: 0 auto;
	position: relative;
	top: 0px;
	z-index:1;
	overflow: hidden;
}

#hdrFlashWrap{/*This houses the main background for the home page Flash navigation*/
	position: relative;
	height:64px !important;
	width: 100%;
	margin:0 0 25px 0; 
	z-index:4;
	overflow: hidden;
}

#flashNav{/*This houses the main Flash navigation*/
	width:960px;
	height:64px !important;
	margin: 0 auto;
	position: relative;
	background: url(/images/affiliates/1/flashnav-bg.gif) 100% 0 no-repeat;
}

#bigscreen{
	position: absolute;
	top: 2px;
	right: 329px;
	width: 97px;
	
}
	
#sm_screen_button, #sm_screen_button_fr, #sm_screen_button_pt, #sm_screen_button_es {
	background-image:url(/images/button_small_screen.gif);
	background-position:0pt 0px;
	background-repeat:no-repeat;
	display:block;
	float:right;
	height:22px;
	width:100px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 5px;
}

#sm_screen_button_fr {
	background-image:url(/fr/images/button_small_screen.gif);
}

#sm_screen_button_pt {
	background-image:url(/pt/images/button_small_screen.gif);
}

#sm_screen_button_es {
	background-image:url(/es/images/button_small_screen.gif);
}

#sm_screen_button:hover, #sm_screen_button_fr:hover, #sm_screen_button_pt:hover, #sm_screen_button_es:hover {
background-position:0pt -30px;
cursor: pointer;
}

#big_screen_button, #big_screen_button_fr, #big_screen_button_pt, #big_screen_button_es {
	background-image:url(/images/button_big_screen.gif);
	background-position:0pt 0px;
	background-repeat:no-repeat;
	display:block;
	float:right;
	height:22px;
	width:100px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 5px;
}

#big_screen_button_fr {
	background-image:url(/images/button_big_screen_fr.gif);
}

#big_screen_button_pt {
	background-image:url(/images/button_big_screen_pt.gif);
}

#big_screen_button_es {
	background-image:url(/images/button_big_screen_es.gif);
}

#big_screen_button:hover, #big_screen_button_fr:hover, #big_screen_button_pt:hover, #big_screen_button_es:hover {
background-position:0pt -30px;
cursor: pointer;
}

	
#logoff{
	position: absolute;
	top: 7px;
	font-weight:bolder;
	right: 0px;
	font-size: 11px;
}
	
#logoff a, #logoff a:link, #logoff a:visited{
  color: #fff;
	display: block;
	border: none;
	text-decoration: none;
	font-size:11px;
}

#logoff a:hover{
  color: #feb62a;
	display: block;
	border: none;
	text-decoration: none;
}


#logoffFlash{
	position: absolute;
	top: 7px;
	font-weight:bolder;
	right: 0px;
	font-size: 11px;
}
	
#logoffFlash a, #logoffFlash a:link, #logoffFlash a:visited{
  color: #fff;
	display: block;
	border: none;
	text-decoration: none;
	font-size:11px;
}

#logoffFlash a:hover{
  color: #feb62a;
	display: block;
	border: none;
	text-decoration: none;
}


#navBg ul#bu_nav{
	position: absolute;
	top: 6px;
	left: 2px;
	display: block;
	list-style: none;
	height:auto;
	width: auto;
	text-align: left;
	overflow: hidden;
}

#navBg ul#bu_nav li{
	display: inline;
	list-style: none;
	margin:0 2px 0 0;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	font-weight: 700;
	color:#feb62a;
	overflow:hidden;
}

#navBg ul#bu_nav li:last-child {
	margin:0;
}
	
#navBg ul#bu_nav li a, #navBg ul#bu_nav li a:link, #navBg ul#bu_nav li a:visited{
		font-family:Arial, Helvetica, sans-serif;
		font-size:11px;
		font-weight: 700;
		color:#fff;
		text-decoration: none;
	}

#navBg ul#bu_nav li a:hover{
		font-family:Arial, Helvetica, sans-serif;
		color:#feb62a;
		text-decoration: none;
	}

#navBg ul#bu_nav li .active{
		color:#fff;
	}
		
.langselect {
top: 1px; background-image: url(/images/language_bg.gif); background-repeat: no-repeat;background-position: 0px 2px; position: absolute; right: 55px; width: 249px; height: 23px;
}

.langselectFlash {
top: 1px; background-image: url(/images/affiliates/1/select-lang-drop-down-flash.gif); background-repeat: no-repeat;background-position: 0px 0px; position: absolute; top: 5px; right: 5px; width: 249px; height: 30px;
}

.langselectfield {
color:#666; font-family:Arial,Helvetica,sans-serif; font-weight: bold; font-size: 10px; margin-top: 3px;
}

.langselectgobutton {
outline: none; margin-top: -3px; vertical-align:top;
}

.langselectlabel {
color:#fff; font-family:Arial,Helvetica,sans-serif; font-weight: bolder; font-size: 11px; padding-left: 7px;
}

.langselectlabelFlash {
color:#fff; font-family:Arial,Helvetica,sans-serif; font-weight: bolder; font-size: 11px; padding-left: 10px; padding-top: 2px;
}		

/* Footer Elements */
	
	/* Nav */
	#ftrBg ul{
	position: absolute;
	top: 48px;
	left: 230px;
	display: block;
	list-style: none;
	height:auto;
	width: 490px;
	text-align: center;
	}
	#ftrBg ul li{
		display: inline;
		list-style: none;
		margin:0 3px 0 0;
		font-family:Arial, Helvetica, sans-serif;
		font-size:12px;
		font-weight: 800;
		color:#b4b4b4;
	}
	#ftrBg ul .noMarg{
		margin:0;
	}
		#ftrBg ul li a, #ftrBg ul li a:link, #ftrBg ul li a:visited{
			font-family:Arial, Helvetica, sans-serif;
			font-size:11px;
			font-weight: 700;
			color:#b4b4b4;
			text-decoration: none;
		}
		#ftrBg ul li a:hover{
			font-family:Arial, Helvetica, sans-serif;
			color:#045aa1;
			text-decoration: none;
		}
		#ftrBg ul li .active{
			color:#5ad0fa;
		}
	
	#ftrBg p{
	position: absolute;
	top: 69px;
	left: 245px;
	height:auto;
	width: 460px;
	text-align: center;
	font-family:Arial, Helvetica, sans-serif;
	font-size:10.5px;
	color:#a1a1a1;
	}

#ftrWrap{
	position: relative;
	top: -8px;
	left:0;
	height:auto;
	width: 936px;
	margin: 0 auto;
}
	
#ftrBg{
	height:auto;
	width: 936px;
	margin: 0 auto;
}

#ftrContent{
	height:auto;
	width: 936px;
	margin: 0 auto;
}

img{
	border: none !important;
}

#disGame{
	display: block;
	position: absolute;
	top: 32px;
	left: 48px;
}

.asterisk {
color: #00529B;
}

.updated {
font-size: 10px;
}

/* BELOW can be used to change the color of the Privacy Policy and Terms of Use links in the footer when necessary */

#ftrContent ul li a.new:link{color:#ff9a00;}
#ftrContent ul li a.new:visited{color:#ff9a00;}
#ftrContent ul li a.new:hover{color:#ffcc00;}
#ftrContent ul li a.new:active{color:#ff9a00;}

#esrb {
	margin-top: 35px; 
	margin-right: 30px;
}

#truste {
	margin: 50px 10px 0px 0px;
}
