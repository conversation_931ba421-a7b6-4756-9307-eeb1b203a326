{"aqua": {"room_id": "916", "music_id": "0", "stamp_group_id": "13", "path": "sub/bootstrap.swf", "name": "Aqua Grabber", "is_as3": "0", "show_player_in_room": "0"}, "astro": {"room_id": "900", "music_id": "0", "stamp_group_id": "14", "path": "astro/bootstrap.swf", "name": "Astro Barrier", "is_as3": "0", "show_player_in_room": "1"}, "balloon": {"room_id": "942", "music_id": "222", "stamp_group_id": "0", "path": "balloon/bootstrap.swf", "name": "Balloon Pop", "is_as3": "0", "show_player_in_room": "0"}, "beans": {"room_id": "901", "music_id": "0", "stamp_group_id": "0", "path": "beans/bootstrap.swf", "name": "Bean Counters", "is_as3": "0", "show_player_in_room": "0"}, "bell": {"room_id": "943", "music_id": "222", "stamp_group_id": "0", "path": "bell/bootstrap.swf", "name": "Ring The Bell", "is_as3": "0", "show_player_in_room": "0"}, "book1": {"room_id": "917", "music_id": "0", "stamp_group_id": "0", "path": "book1/bootstrap.swf", "name": "Paint By Letters", "is_as3": "0", "show_player_in_room": "0"}, "book2": {"room_id": "918", "music_id": "0", "stamp_group_id": "0", "path": "book2/BurntOutBulbs.swf", "name": "Paint By Letters", "is_as3": "0", "show_player_in_room": "0"}, "book3": {"room_id": "919", "music_id": "0", "stamp_group_id": "0", "path": "book3/LimeGreenDojoClean.swf", "name": "Paint By Letters", "is_as3": "0", "show_player_in_room": "0"}, "card": {"room_id": "998", "music_id": "116", "stamp_group_id": "38", "path": "card/bootstrap.swf", "name": "Card-Jitsu", "is_as3": "0", "show_player_in_room": "0"}, "cart": {"room_id": "905", "music_id": "105", "stamp_group_id": "28", "path": "mine/bootstrap.swf", "name": "<PERSON><PERSON>", "is_as3": "0", "show_player_in_room": "0"}, "dancing": {"room_id": "952", "music_id": "0", "stamp_group_id": "0", "path": "dancing/dance.swf", "name": "Dance Contest", "is_as3": "0", "show_player_in_room": "0"}, "feed": {"room_id": "944", "music_id": "222", "stamp_group_id": "0", "path": "feed/bootstrap.swf", "name": "Feed <PERSON>", "is_as3": "0", "show_player_in_room": "0"}, "fire": {"room_id": "997", "music_id": "118", "stamp_group_id": "32", "path": "fire/fire.swf", "name": "Card-Jitsu Fire", "is_as3": "0", "show_player_in_room": "0"}, "firesensei": {"room_id": "953", "music_id": "0", "stamp_group_id": "0", "path": "senseiFire/bootstrap.swf", "name": "Card-Jitsu Fire", "is_as3": "0", "show_player_in_room": "0"}, "fish": {"room_id": "904", "music_id": "0", "stamp_group_id": "52", "path": "fish/bootstrap.swf", "name": "Ice Fishing", "is_as3": "0", "show_player_in_room": "0"}, "four": {"room_id": "0", "music_id": "0", "stamp_group_id": "0", "path": "four/FindFour.swf", "name": "Find Four", "is_as3": "0", "show_player_in_room": "0"}, "hydro": {"room_id": "903", "music_id": "100", "stamp_group_id": "0", "path": "hydro/bootstrap.swf", "name": "Hydro-Hopper", "is_as3": "0", "show_player_in_room": "0"}, "icejam": {"room_id": "960", "music_id": "0", "stamp_group_id": "0", "path": "icejam/bootstrap.swf", "name": "Challenge", "is_as3": "0", "show_player_in_room": "0"}, "igloo_card": {"room_id": "994", "music_id": "116", "stamp_group_id": "38", "path": "card/bootstrap.swf", "name": "Igloo Card-Jitsu", "is_as3": "0", "show_player_in_room": "0"}, "jetpack": {"room_id": "906", "music_id": "110", "stamp_group_id": "11", "path": "jetpack/bootstrap.swf", "name": "Jet Pack Adventure", "is_as3": "0", "show_player_in_room": "0"}, "mancala": {"room_id": "0", "music_id": "0", "stamp_group_id": "0", "path": "mancala/Mancala.swf", "name": "Mancala", "is_as3": "0", "show_player_in_room": "0"}, "memory": {"room_id": "945", "music_id": "222", "stamp_group_id": "0", "path": "memory/bootstrap.swf", "name": "Memory", "is_as3": "0", "show_player_in_room": "0"}, "mission1": {"room_id": "907", "music_id": "0", "stamp_group_id": "0", "path": "quests/q1/quest.swf", "name": "Case of the Missing <PERSON><PERSON><PERSON>", "is_as3": "0", "show_player_in_room": "0"}, "mission10": {"room_id": "923", "music_id": "0", "stamp_group_id": "0", "path": "quests/q10/quest.swf", "name": "Waddle Squad", "is_as3": "0", "show_player_in_room": "0"}, "mission11": {"room_id": "927", "music_id": "0", "stamp_group_id": "0", "path": "quests/q11/quest.swf", "name": "<PERSON><PERSON><PERSON>", "is_as3": "0", "show_player_in_room": "0"}, "mission2": {"room_id": "908", "music_id": "0", "stamp_group_id": "0", "path": "quests/q2/quest.swf", "name": "G's Secret Mission", "is_as3": "0", "show_player_in_room": "0"}, "mission3": {"room_id": "911", "music_id": "0", "stamp_group_id": "0", "path": "quests/q3/quest.swf", "name": "Case of the Missing Coins", "is_as3": "0", "show_player_in_room": "0"}, "mission4": {"room_id": "913", "music_id": "0", "stamp_group_id": "0", "path": "quests/q4/quest.swf", "name": "Avalanche Rescue", "is_as3": "0", "show_player_in_room": "0"}, "mission5": {"room_id": "914", "music_id": "0", "stamp_group_id": "0", "path": "quests/q5/quest.swf", "name": "Secret of the Fur", "is_as3": "0", "show_player_in_room": "0"}, "mission6": {"room_id": "915", "music_id": "0", "stamp_group_id": "0", "path": "quests/q6/quest.swf", "name": "Questions for a Crab", "is_as3": "0", "show_player_in_room": "0"}, "mission7": {"room_id": "920", "music_id": "0", "stamp_group_id": "0", "path": "quests/q7/quest.swf", "name": "Clockwork Repairs", "is_as3": "0", "show_player_in_room": "0"}, "mission8": {"room_id": "921", "music_id": "0", "stamp_group_id": "0", "path": "quests/q8/quest.swf", "name": "Mysterious Tremors", "is_as3": "0", "show_player_in_room": "0"}, "mission9": {"room_id": "922", "music_id": "0", "stamp_group_id": "0", "path": "quests/q9/quest.swf", "name": "Operation: Spy and Seek", "is_as3": "0", "show_player_in_room": "0"}, "mixmaster": {"room_id": "926", "music_id": "0", "stamp_group_id": "0", "path": "mixmaster/ChooseSong.swf", "name": "DJ3K", "is_as3": "0", "show_player_in_room": "0"}, "paddle": {"room_id": "946", "music_id": "222", "stamp_group_id": "0", "path": "paddle/bootstrap.swf", "name": "<PERSON><PERSON><PERSON>", "is_as3": "0", "show_player_in_room": "0"}, "pizzatron": {"room_id": "910", "music_id": "106", "stamp_group_id": "54", "path": "pizzatron/bootstrap.swf", "name": "Pizzatron 3000", "is_as3": "0", "show_player_in_room": "0"}, "placeholder": {"room_id": "0", "music_id": "0", "stamp_group_id": "0", "path": "game/placeholder", "name": "placeholder", "is_as3": "0", "show_player_in_room": "0"}, "placeholder2": {"room_id": "0", "music_id": "0", "stamp_group_id": "0", "path": "game/placeholder", "name": "placeholder2", "is_as3": "0", "show_player_in_room": "0"}, "rescue": {"room_id": "949", "music_id": "0", "stamp_group_id": "19", "path": "rescue/bootstrap.swf", "name": "Puffle Rescue", "is_as3": "0", "show_player_in_room": "0"}, "roundup": {"room_id": "902", "music_id": "102", "stamp_group_id": "0", "path": "roundup/bootstrap.swf", "name": "Puffle Round-Up", "is_as3": "0", "show_player_in_room": "0"}, "sensei": {"room_id": "951", "music_id": "0", "stamp_group_id": "0", "path": "sensei/bootstrap.swf", "name": "Card-Jitsu", "is_as3": "0", "show_player_in_room": "0"}, "shuffle": {"room_id": "947", "music_id": "222", "stamp_group_id": "0", "path": "shuffle/bootstrap.swf", "name": "<PERSON><PERSON><PERSON>", "is_as3": "0", "show_player_in_room": "0"}, "sled": {"room_id": "999", "music_id": "117", "stamp_group_id": "0", "path": "sled/SledRacer.swf", "name": "Sled Racing", "is_as3": "0", "show_player_in_room": "0"}, "soaker": {"room_id": "941", "music_id": "222", "stamp_group_id": "0", "path": "soaker/bootstrap.swf", "name": "<PERSON><PERSON><PERSON>", "is_as3": "0", "show_player_in_room": "0"}, "spin": {"room_id": "948", "music_id": "222", "stamp_group_id": "0", "path": "spin/bootstrap.swf", "name": "Spin The Wheel", "is_as3": "0", "show_player_in_room": "0"}, "systemdefend": {"room_id": "950", "music_id": "0", "stamp_group_id": "46", "path": "tower/main.swf", "name": "System Defender", "is_as3": "0", "show_player_in_room": "0"}, "thinice": {"room_id": "909", "music_id": "111", "stamp_group_id": "16", "path": "thinice/bootstrap.swf", "name": "Thin Ice", "is_as3": "0", "show_player_in_room": "1"}, "treasurehunt": {"room_id": "0", "music_id": "0", "stamp_group_id": "56", "path": "treasurehunt/TreasureHunt.swf", "name": "Treasure Hunt", "is_as3": "0", "show_player_in_room": "0"}, "water": {"room_id": "995", "music_id": "0", "stamp_group_id": "34", "path": "cardjitsu/water/water.swf", "name": "Card-Jitsu Water", "is_as3": "0", "show_player_in_room": "0"}, "watersensei": {"room_id": "954", "music_id": "0", "stamp_group_id": "0", "path": "cardjitsu/watersensei/bootstrap.swf", "name": "Card-Jitsu Water", "is_as3": "0", "show_player_in_room": "0"}, "waves": {"room_id": "912", "music_id": "0", "stamp_group_id": "15", "path": "waves/bootstrap.swf", "name": "Catchin' Waves", "is_as3": "0", "show_player_in_room": "0"}, "cannon": {"name": "Puffle Launch", "room_id": "955", "music_id": "0", "stamp_group_id": "48", "path": "cannon/cannon.swf", "is_as3": "0", "show_player_in_room": "0"}}