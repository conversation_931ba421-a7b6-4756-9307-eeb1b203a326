#!/usr/bin/env node

/**
 * Email Setup Script for Yukon Server
 * 
 * This script helps you configure email settings for your Raspberry Pi
 * Supports Gmail, Outlook, Yahoo, and custom SMTP servers
 */

const fs = require('fs')
const path = require('path')
const readline = require('readline')

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
})

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve)
    })
}

async function main() {
    console.log('🐧 Yukon Email Setup for Raspberry Pi')
    console.log('=====================================\n')

    console.log('This script will help you configure email sending for:')
    console.log('✅ Account verification emails')
    console.log('✅ Password reset emails')
    console.log('✅ System notifications\n')

    // Choose email service
    console.log('Choose your email service:')
    console.log('1. Gmail (recommended)')
    console.log('2. Outlook/Hotmail')
    console.log('3. Yahoo Mail')
    console.log('4. Custom SMTP server')
    console.log('5. Disable email (not recommended)')

    const serviceChoice = await question('\nEnter your choice (1-5): ')

    let emailConfig = {
        enabled: true,
        verification: { enabled: true, expiry: 86400 },
        passwordReset: { enabled: true, expiry: 3600 }
    }

    switch (serviceChoice) {
        case '1':
            emailConfig = await setupGmail(emailConfig)
            break
        case '2':
            emailConfig = await setupOutlook(emailConfig)
            break
        case '3':
            emailConfig = await setupYahoo(emailConfig)
            break
        case '4':
            emailConfig = await setupCustom(emailConfig)
            break
        case '5':
            emailConfig.enabled = false
            console.log('⚠️  Email disabled. Users will not receive verification emails.')
            break
        default:
            console.log('❌ Invalid choice. Exiting.')
            process.exit(1)
    }

    if (emailConfig.enabled) {
        // Test email
        const testEmail = await question('\nEnter an email to test with (or press Enter to skip): ')
        if (testEmail.trim()) {
            await testEmailConfig(emailConfig, testEmail.trim())
        }
    }

    // Save configuration
    await saveConfig(emailConfig)

    console.log('\n✅ Email configuration saved!')
    console.log('\nNext steps:')
    console.log('1. Run: npm install (to install nodemailer)')
    console.log('2. Restart your Yukon server')
    console.log('3. Test registration with email verification')

    rl.close()
}

async function setupGmail(config) {
    console.log('\n📧 Gmail Setup')
    console.log('==============')
    console.log('You need to create an "App Password" for Gmail:')
    console.log('1. Go to https://myaccount.google.com/security')
    console.log('2. Enable 2-Factor Authentication')
    console.log('3. Generate an "App Password" for "Mail"')
    console.log('4. Use that password below (not your regular password)\n')

    const email = await question('Gmail address: ')
    const password = await question('App password: ')
    const fromName = await question('From name (e.g., "Yukon Club Penguin"): ')

    return {
        ...config,
        service: 'gmail',
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        auth: { user: email, pass: password },
        from: `${fromName} <${email}>`
    }
}

async function setupOutlook(config) {
    console.log('\n📧 Outlook Setup')
    console.log('================')

    const email = await question('Outlook/Hotmail address: ')
    const password = await question('Password: ')
    const fromName = await question('From name (e.g., "Yukon Club Penguin"): ')

    return {
        ...config,
        service: 'hotmail',
        host: 'smtp-mail.outlook.com',
        port: 587,
        secure: false,
        auth: { user: email, pass: password },
        from: `${fromName} <${email}>`
    }
}

async function setupYahoo(config) {
    console.log('\n📧 Yahoo Setup')
    console.log('==============')
    console.log('You need to create an "App Password" for Yahoo:')
    console.log('1. Go to Yahoo Account Security')
    console.log('2. Generate an "App Password"')
    console.log('3. Use that password below\n')

    const email = await question('Yahoo email address: ')
    const password = await question('App password: ')
    const fromName = await question('From name (e.g., "Yukon Club Penguin"): ')

    return {
        ...config,
        host: 'smtp.mail.yahoo.com',
        port: 587,
        secure: false,
        auth: { user: email, pass: password },
        from: `${fromName} <${email}>`
    }
}

async function setupCustom(config) {
    console.log('\n📧 Custom SMTP Setup')
    console.log('====================')

    const host = await question('SMTP host: ')
    const port = await question('SMTP port (usually 587 or 465): ')
    const secure = await question('Use SSL/TLS? (y/n): ')
    const email = await question('Email address: ')
    const password = await question('Password: ')
    const fromName = await question('From name: ')

    return {
        ...config,
        host: host,
        port: parseInt(port),
        secure: secure.toLowerCase() === 'y',
        auth: { user: email, pass: password },
        from: `${fromName} <${email}>`
    }
}

async function testEmailConfig(config, testEmail) {
    console.log('\n🧪 Testing email configuration...')
    
    try {
        const nodemailer = require('nodemailer')
        
        const transporter = nodemailer.createTransporter({
            host: config.host,
            port: config.port,
            secure: config.secure,
            auth: config.auth
        })

        await transporter.sendMail({
            from: config.from,
            to: testEmail,
            subject: 'Yukon Email Test',
            html: '<h2>🎉 Success!</h2><p>Your Yukon email configuration is working!</p>'
        })

        console.log('✅ Test email sent successfully!')
        
    } catch (error) {
        console.log('❌ Test email failed:', error.message)
        console.log('Please check your settings and try again.')
    }
}

async function saveConfig(emailConfig) {
    const configPath = path.join(__dirname, 'config', 'config.json')
    
    try {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))
        config.email = emailConfig
        
        fs.writeFileSync(configPath, JSON.stringify(config, null, 4))
        
    } catch (error) {
        console.error('❌ Failed to save configuration:', error.message)
        console.log('\nManually add this to your config.json:')
        console.log(JSON.stringify({ email: emailConfig }, null, 4))
    }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
    console.log('\n\n👋 Setup cancelled.')
    rl.close()
    process.exit(0)
})

main().catch(console.error)
