import nodemailer from 'nodemailer'
import crypto from 'crypto'
import fs from 'fs'
import path from 'path'

export default class EmailService {
    constructor(config, db) {
        this.config = config.email
        this.db = db
        this.transporter = null
        
        if (this.config.enabled) {
            this.setupTransporter()
        }
    }

    setupTransporter() {
        // Support multiple email services
        const transportConfig = {
            host: this.config.host,
            port: this.config.port,
            secure: this.config.secure,
            auth: {
                user: this.config.auth.user,
                pass: this.config.auth.pass
            }
        }

        // For Gmail, use service shortcut
        if (this.config.service === 'gmail') {
            transportConfig.service = 'gmail'
        }

        this.transporter = nodemailer.createTransporter(transportConfig)
        
        // Verify connection
        this.transporter.verify((error, success) => {
            if (error) {
                console.error('❌ Email service connection failed:', error)
            } else {
                console.log('✅ Email service ready')
            }
        })
    }

    async sendVerificationEmail(user, baseUrl) {
        if (!this.config.enabled || !this.config.verification.enabled) {
            return { success: false, message: 'Email verification disabled' }
        }

        try {
            // Generate verification token
            const token = crypto.randomBytes(32).toString('hex')
            const expiry = new Date(Date.now() + this.config.verification.expiry * 1000)

            // Save token to database
            await this.db.users.update({
                emailVerificationToken: token,
                emailVerificationExpiry: expiry
            }, {
                where: { id: user.id }
            })

            // Create verification URL
            const verificationUrl = `${baseUrl}/verify-email?token=${token}&user=${user.id}`

            // Load email template
            const template = this.loadTemplate('verification', {
                username: user.username,
                verificationUrl: verificationUrl,
                expiry: Math.floor(this.config.verification.expiry / 3600) // hours
            })

            // Send email
            const mailOptions = {
                from: this.config.from,
                to: user.email,
                subject: 'Verify Your Yukon Account',
                html: template
            }

            await this.transporter.sendMail(mailOptions)
            
            return { success: true, message: 'Verification email sent' }

        } catch (error) {
            console.error('Email send error:', error)
            return { success: false, message: 'Failed to send verification email' }
        }
    }

    async sendPasswordResetEmail(user, baseUrl) {
        if (!this.config.enabled || !this.config.passwordReset.enabled) {
            return { success: false, message: 'Password reset disabled' }
        }

        try {
            // Generate reset token
            const token = crypto.randomBytes(32).toString('hex')
            const expiry = new Date(Date.now() + this.config.passwordReset.expiry * 1000)

            // Save token to database
            await this.db.users.update({
                passwordResetToken: token,
                passwordResetExpiry: expiry
            }, {
                where: { id: user.id }
            })

            // Create reset URL
            const resetUrl = `${baseUrl}/reset-password?token=${token}&user=${user.id}`

            // Load email template
            const template = this.loadTemplate('password-reset', {
                username: user.username,
                resetUrl: resetUrl,
                expiry: Math.floor(this.config.passwordReset.expiry / 60) // minutes
            })

            // Send email
            const mailOptions = {
                from: this.config.from,
                to: user.email,
                subject: 'Reset Your Yukon Password',
                html: template
            }

            await this.transporter.sendMail(mailOptions)
            
            return { success: true, message: 'Password reset email sent' }

        } catch (error) {
            console.error('Email send error:', error)
            return { success: false, message: 'Failed to send password reset email' }
        }
    }

    async verifyEmail(userId, token) {
        try {
            const user = await this.db.users.findOne({
                where: {
                    id: userId,
                    emailVerificationToken: token
                }
            })

            if (!user) {
                return { success: false, message: 'Invalid verification token' }
            }

            if (new Date() > user.emailVerificationExpiry) {
                return { success: false, message: 'Verification token expired' }
            }

            // Mark email as verified
            await this.db.users.update({
                emailVerified: 1,
                emailVerificationToken: null,
                emailVerificationExpiry: null
            }, {
                where: { id: userId }
            })

            return { success: true, message: 'Email verified successfully' }

        } catch (error) {
            console.error('Email verification error:', error)
            return { success: false, message: 'Verification failed' }
        }
    }

    async validatePasswordResetToken(userId, token) {
        try {
            const user = await this.db.users.findOne({
                where: {
                    id: userId,
                    passwordResetToken: token
                }
            })

            if (!user) {
                return { success: false, message: 'Invalid reset token' }
            }

            if (new Date() > user.passwordResetExpiry) {
                return { success: false, message: 'Reset token expired' }
            }

            return { success: true, user: user }

        } catch (error) {
            console.error('Token validation error:', error)
            return { success: false, message: 'Token validation failed' }
        }
    }

    loadTemplate(templateName, variables) {
        try {
            const templatePath = path.join(__dirname, '../templates/email', `${templateName}.html`)
            let template = fs.readFileSync(templatePath, 'utf8')

            // Replace variables
            for (const [key, value] of Object.entries(variables)) {
                template = template.replace(new RegExp(`{{${key}}}`, 'g'), value)
            }

            return template

        } catch (error) {
            console.error('Template load error:', error)
            return this.getDefaultTemplate(templateName, variables)
        }
    }

    getDefaultTemplate(templateName, variables) {
        if (templateName === 'verification') {
            return `
                <h2>Welcome to Yukon Club Penguin!</h2>
                <p>Hi ${variables.username},</p>
                <p>Please click the link below to verify your email address:</p>
                <a href="${variables.verificationUrl}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Verify Email</a>
                <p>This link will expire in ${variables.expiry} hours.</p>
                <p>If you didn't create this account, please ignore this email.</p>
            `
        } else if (templateName === 'password-reset') {
            return `
                <h2>Password Reset Request</h2>
                <p>Hi ${variables.username},</p>
                <p>Click the link below to reset your password:</p>
                <a href="${variables.resetUrl}" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a>
                <p>This link will expire in ${variables.expiry} minutes.</p>
                <p>If you didn't request this reset, please ignore this email.</p>
            `
        }
        return '<p>Email template not found</p>'
    }

    // Test email functionality
    async sendTestEmail(to) {
        if (!this.config.enabled) {
            return { success: false, message: 'Email service disabled' }
        }

        try {
            const mailOptions = {
                from: this.config.from,
                to: to,
                subject: 'Yukon Email Test',
                html: '<h2>Email service is working!</h2><p>Your Yukon server can send emails successfully.</p>'
            }

            await this.transporter.sendMail(mailOptions)
            return { success: true, message: 'Test email sent successfully' }

        } catch (error) {
            console.error('Test email error:', error)
            return { success: false, message: 'Test email failed' }
        }
    }
}
