{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "overrides": [], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "prefer-arrow"], "rules": {"prefer-arrow/prefer-arrow-functions": ["error", {"disallowPrototype": true, "singleReturnOnly": false, "classPropertiesAllowed": false}], "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}], "func-style": ["error", "expression", {"allowArrowFunctions": true}], "semi": [2, "always"]}}