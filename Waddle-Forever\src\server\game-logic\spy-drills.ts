enum SpyDrillGame {
  GridCommand = 1,
  ChipMaze = 2,
  CircuitMatch = 3,
  CodeBreak = 4,
  TheNavigator = 5,
  Firewall = 6,
  CodeDecrypt = 7,
  RadarStrike = 8,
  RhythmLock = 9,
  Tumblers = 10
};

/** A huge sample of "experimental" data collected from videos which is used to simulate the RNG of the game */
export const SPY_DRILLS_DATA: Array<[[SpyDrillGame, SpyDrillGame, SpyDrillGame], number]> = [
  [[2, 1, 9], 5],
  [[2, 5, 9], 5],
  [[2, 8, 10], 5],
  [[2, 8, 10], 5],
  [[3, 2, 1], 2],
  [[3, 2, 1], 2],
  [[3, 2, 8], 3],
  [[3, 2, 8], 3],
  [[3, 4, 7], 1],
  [[3, 4, 8], 2],
  [[3, 4, 8], 2],
  [[3, 4, 1], 1],
  [[3, 7, 2], 2],
  [[3, 7, 2], 2],
  [[3, 7, 1], 2],
  [[3, 7, 5], 2],
  [[3, 7, 8], 2],
  [[3, 7, 8], 2],
  [[3, 7, 9], 2],
  [[3, 7, 10], 3],
  [[3, 7, 10], 3],
  [[3, 6, 2], 2],
  [[3, 6, 2], 2],
  [[3, 6, 2], 2],
  [[3, 6, 7], 1],
  [[3, 6, 5], 2],
  [[3, 6, 5], 2],
  [[3, 6, 5], 2],
  [[3, 6, 5], 2],
  [[3, 6, 5], 2],
  [[3, 6, 8], 2],
  [[3, 6, 9], 2],
  [[3, 6, 9], 2],
  [[3, 6, 9], 2],
  [[3, 6, 10], 3],
  [[3, 6, 10], 3],
  [[3, 1, 2], 2],
  [[3, 1, 8], 3],
  [[3, 1, 9], 3],
  [[3, 1, 10], 4],
  [[3, 1, 10], 4],
  [[3, 5, 2], 2],
  [[3, 5, 2], 2],
  [[3, 5, 1], 2],
  [[3, 5, 1], 2],
  [[3, 5, 8], 3],
  [[3, 5, 8], 3],
  [[3, 5, 10], 4],
  [[3, 8, 9], 4],
  [[3, 9, 10], 5],
  [[4, 7, 2], 2],
  [[4, 7, 1], 2],
  [[4, 7, 1], 2],
  [[4, 7, 1], 2],
  [[4, 7, 5], 2],
  [[4, 7, 8], 2],
  [[4, 7, 8], 2],
  [[4, 7, 8], 2],
  [[4, 7, 10], 3],
  [[4, 7, 10], 3],
  [[4, 2, 5], 2],
  [[4, 2, 8], 3],
  [[4, 2, 8], 3],
  [[4, 2, 9], 3],
  [[4, 2, 10], 4],
  [[4, 3, 2], 1],
  [[4, 3, 2], 1],
  [[4, 3, 2], 1],
  [[4, 3, 7], 1],
  [[4, 3, 6], 1],
  [[4, 3, 6], 1],
  [[4, 3, 1], 1],
  [[4, 3, 5], 1],
  [[4, 3, 5], 1],
  [[4, 3, 5], 1],
  [[4, 6, 2], 2],
  [[4, 6, 7], 1],
  [[4, 6, 1], 2],
  [[4, 6, 1], 2],
  [[4, 6, 8], 2],
  [[4, 6, 8], 2],
  [[4, 1, 2], 2],
  [[4, 1, 5], 2],
  [[4, 1, 8], 3],
  [[4, 1, 9], 3],
  [[4, 1, 10], 4],
  [[4, 5, 1], 2],
  [[4, 5, 9], 3],
  [[7, 2, 1], 3],
  [[7, 2, 5], 3],
  [[7, 2, 8], 4],
  [[7, 2, 9], 4],
  [[7, 2, 9], 4],
  [[7, 6, 2], 2],
  [[7, 6, 1], 2],
  [[7, 6, 8], 3],
  [[7, 6, 9], 3],
  [[7, 6, 9], 3],
  [[7, 6, 10], 4],
  [[7, 9, 10], 5],
  [[6, 2, 1], 3],
  [[6, 2, 1], 3],
  [[6, 2, 5], 3],
  [[6, 2, 9], 4],
  [[6, 2, 9], 4],
  [[6, 7, 1], 2],
  [[6, 7, 5], 2],
  [[6, 7, 5], 2],
  [[6, 1, 9], 4],
  [[6, 5, 2], 3],
  [[6, 5, 2], 3],
  [[6, 5, 1], 3],
  [[6, 5, 8], 4],
  [[6, 5, 10], 5],
  [[1, 5, 10], 5],
  [[1, 5, 8], 5],
  [[1, 5, 9], 5],
  [[1, 8, 10], 5],
  [[1, 8, 10], 5],
  [[5, 2, 10], 5],
  [[5, 1, 8], 5],
  [[5, 1, 9], 5],
  [[5, 1, 2], 4],
  [[5, 1, 10], 5],
  [[5, 1, 10], 5],
  [[5, 8, 10], 5],
  [[5, 8, 10], 5],
  [[5, 9, 10], 5],
  [[5, 9, 10], 5]
];