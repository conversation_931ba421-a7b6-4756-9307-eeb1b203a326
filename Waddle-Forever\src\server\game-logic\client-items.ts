const CRUMBS_86 = new Set([  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  401,
  402,
  403,
  404,
  405,
  406,
  407,
  408,
  409,
  410,
  411,
  412,
  413,
  414,
  415,
  417,
  418,
  419,
  420,
  421,
  422,
  423,
  424,
  425,
  426,
  427,
  428,
  429,
  430,
  431,
  432,
  433,
  434,
  435,
  436,
  437,
  438,
  439,
  440,
  441,
  443,
  444,
  445,
  446,
  447,
  448,
  449,
  450,
  451,
  452,
  453,
  454,
  455,
  456,
  457,
  458,
  460,
  461,
  462,
  463,
  464,
  465,
  466,
  467,
  468,
  469,
  470,
  471,
  472,
  473,
  474,
  475,
  476,
  477,
  478,
  479,
  480,
  481,
  482,
  483,
  484,
  485,
  486,
  487,
  488,
  489,
  490,
  491,
  492,
  493,
  494,
  495,
  496,
  497,
  498,
  499,
  650,
  651,
  652,
  653,
  654,
  655,
  656,
  657,
  658,
  659,
  660,
  661,
  662,
  663,
  664,
  101,
  102,
  103,
  105,
  106,
  107,
  108,
  109,
  110,
  111,
  112,
  113,
  114,
  116,
  117,
  118,
  119,
  120,
  121,
  122,
  123,
  124,
  130,
  131,
  132,
  133,
  134,
  135,
  136,
  137,
  138,
  162,
  166,
  167,
  168,
  169,
  170,
  171,
  172,
  173,
  174,
  175,
  176,
  177,
  179,
  180,
  181,
  182,
  183,
  184,
  185,
  186,
  191,
  192,
  193,
  194,
  195,
  214,
  216,
  301,
  302,
  303,
  304,
  305,
  306,
  307,
  308,
  309,
  310,
  312,
  313,
  314,
  315,
  201,
  202,
  203,
  204,
  205,
  206,
  207,
  208,
  209,
  210,
  211,
  212,
  213,
  215,
  217,
  218,
  219,
  221,
  222,
  223,
  224,
  225,
  226,
  227,
  228,
  229,
  230,
  231,
  232,
  235,
  236,
  237,
  238,
  239,
  240,
  241,
  242,
  243,
  244,
  245,
  246,
  247,
  248,
  249,
  250,
  251,
  252,
  253,
  254,
  255,
  256,
  257,
  259,
  258,
  260,
  261,
  262,
  263,
  264,
  265,
  266,
  267,
  268,
  269,
  270,
  271,
  272,
  273,
  274,
  275,
  276,
  277,
  278,
  279,
  280,
  281,
  282,
  283,
  284,
  285,
  286,
  287,
  288,
  289,
  290,
  291,
  292,
  293,
  294,
  295,
  296,
  297,
  298,
  299,
  760,
  761,
  762,
  763,
  764,
  765,
  766,
  767,
  768,
  769,
  770,
  771,
  772,
  773,
  774,
  775,
  778,
  780,
  781,
  782,
  220,
  233,
  234,
  321,
  322,
  323,
  324,
  325,
  326,
  327,
  328,
  329,
  330,
  331,
  332,
  333,
  334,
  335,
  336,
  337,
  338,
  339,
  340,
  341,
  342,
  343,
  344,
  345,
  346,
  347,
  348,
  349,
  350,
  351,
  352,
  353,
  354,
  355,
  357,
  358,
  359,
  360,
  361,
  362,
  363,
  364,
  365,
  366,
  367,
  368,
  369,
  370,
  371,
  372,
  373,
  374,
  375,
  376,
  377,
  378,
  379,
  380,
  500,
  501,
  502,
  503,
  504,
  505,
  506,
  507,
  508,
  509,
  510,
  511,
  512,
  513,
  514,
  515,
  516,
  517,
  518,
  519,
  520,
  521,
  522,
  523,
  524,
  525,
  526,
  527,
  528,
  529,
  530,
  531,
  532,
  533,
  534,
  550,
  551,
  552,
  553,
  554,
  555,
  556,
  557,
  558,
  559,
  560,
  561,
  562,
  563,
  564,
  565,
  566,
  567,
  568,
  569,
  570,
  571,
  572,
  573,
  574,
  575,
  576,
  577,
  578,
  579,
  580,
  581,
  582,
  583,
  584,
  585,
  586,
  587,
  588,
  589,
  590,
  591,
  592,
  593,
  594,
  595,
  596,
  597,
  598,
  599,
  600,
  601,
  602,
  603,
  604,
  605,
  606,
  701,
  702,
  703,
  711,
  712,
  720,
  721,
  722,
  750,
  751,
  752,
  753,
  754,
  755,
  756,
  800,
  801,
  802,
  803,
  804,
  805,
  806,
  807,
  808,
  809,
  810,
  811,
  812,
  813,
  814,
  901,
  902,
  903,
  904,
  905,
  906,
  907,
  908,
  909,
  910,
  911,
  912,
  913,
  914,
  915,
  916,
  917,
  918,
  919,
  920,
  921,
  922,
  923,
  924,
  925,
  926,
  927,
  928,
  929,
  930,
  931,
  932,
  933,
  934,
  935,
  936,
  937,
  938,
  939,
  940,
  941,
  942,
  943,
  944,
  945,
  946,
  947,
  948,
  949,
  950,
  951,
  952,
  953,
  954,
  955,
  956,
  957,
  958,
  959,
  960,
  961,
  962,
  963,
  964,
  965,
  966,
  967,
  968,
  969,
  970,
  971,
  972,
  973,
  974,
  975,
  976,
  977,
  978,
  979,
  980,
  981,
  982,
  983,
  984]);

export const OLD_CLIENT_ITEMS: Record<number, Set<number>> = {
  291:new Set([
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    401,
    403,
    408,
    422,
    451,
    452,
    404,
    481,
    405,
    412,
    410,
    413,
    484,
    417,
    453,
    418,
    402,
    414,
    420,
    406,
    421,
    456,
    419,
    424,
    425,
    407,
    423,
    101,
    102,
    103,
    106,
    107,
    108,
    109,
    110,
    131,
    171,
    172,
    173,
    174,
    175,
    214,
    216,
    176,
    177,
    181,
    201,
    212,
    219,
    222,
    221,
    244,
    252,
    301,
    235,
    237,
    238,
    240,
    263,
    262,
    218,
    253,
    261,
    220,
    233,
    234,
    351,
    352,
    363,
    366,
    500,
    501,
    502,
    503,
    504,
    505,
    506,
    507,
    508,
    509,
    510,
    511,
    512,
    513,
    514,
    515,
    516,
    517,
    518,
    519,
    520,
    521,
    522,
    523,
    524,
    550,
    551,
    800,
    104,
    222
  ]),
  299: new Set([
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    401,
    403,
    408,
    422,
    451,
    452,
    404,
    481,
    405,
    412,
    410,
    413,
    484,
    417,
    453,
    418,
    402,
    414,
    420,
    406,
    421,
    456,
    419,
    424,
    425,
    407,
    423,
    427,
    101,
    102,
    103,
    106,
    107,
    108,
    109,
    110,
    131,
    171,
    172,
    173,
    174,
    175,
    214,
    216,
    176,
    177,
    181,
    201,
    212,
    219,
    221,
    222,
    224,
    244,
    252,
    301,
    235,
    236,
    237,
    238,
    240,
    263,
    262,
    218,
    253,
    261,
    220,
    233,
    234,
    351,
    352,
    362,
    363,
    366,
    500,
    501,
    502,
    503,
    504,
    505,
    506,
    507,
    508,
    509,
    510,
    511,
    512,
    513,
    514,
    515,
    516,
    517,
    518,
    519,
    520,
    521,
    522,
    523,
    524,
    550,
    551,
    552,
    553,
    800
  ]),
  339: new Set([
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    401,
    403,
    408,
    422,
    451,
    452,
    404,
    481,
    405,
    412,
    410,
    413,
    484,
    417,
    453,
    418,
    402,
    414,
    420,
    406,
    409,
    421,
    456,
    419,
    424,
    425,
    407,
    423,
    427,
    429,
    457,
    101,
    102,
    103,
    106,
    107,
    108,
    109,
    110,
    131,
    171,
    172,
    173,
    174,
    175,
    214,
    216,
    176,
    177,
    181,
    302,
    301,
    194,
    193,
    201,
    212,
    219,
    221,
    222,
    224,
    244,
    252,
    235,
    236,
    237,
    238,
    240,
    263,
    262,
    218,
    253,
    261,
    215,
    267,
    269,
    270,
    272,
    273,
    274,
    220,
    233,
    234,
    323,
    325,
    351,
    352,
    362,
    363,
    366,
    433,
    434,
    192,
    217,
    275,
    327,
    368,
    329,
    435,
    436,
    488,
    461,
    462,
    463,
    254,
    255,
    277,
    278,
    500,
    501,
    502,
    503,
    504,
    505,
    506,
    507,
    508,
    509,
    510,
    511,
    512,
    513,
    514,
    515,
    516,
    517,
    518,
    519,
    520,
    521,
    522,
    523,
    524,
    525,
    526,
    527,
    528,
    529,
    530,
    550,
    551,
    552,
    553,
    554,
    555,
    556,
    557,
    558,
    559,
    560,
    561,
    800,
    901,
    902,
    903,
    904,
    905,
    906,
    907,
    908,
    909,
    910,
    911,
    912,
    913,
    914,
    915,
    916,
    917,
    918,
    919,
    920,
    921
  ]),
  506: CRUMBS_86,
  604: CRUMBS_86
};