/** Module for the files of all music in the game */

import { IdRefMap } from "./changes";

/** Base files for all the music */
export const MUSIC_IDS: IdRefMap = {
  1: 'archives:Music1.swf',
  2: 'archives:Music2.swf',
  5: 'slegacy:media/play/v2/content/global/music/5.swf',
  6: 'slegacy:media/play/v2/content/global/music/6.swf',
  7: 'archives:Music7.swf',
  10: 'archives:Music10.swf',
  11: 'archives:Music11.swf',
  20: 'archives:Music20.swf',
  21: 'archives:Music21.swf',
  22: 'archives:Music22.swf',
  23: 'slegacy:media/play/v2/content/global/music/23.swf',
  24: 'slegacy:media/play/v2/content/global/music/24.swf',
  30: 'archives:Music30.swf',
  31: 'archives:Music31.swf',
  32: 'archives:Music32.swf',
  33: 'archives:Music33.swf',
  34: 'archives:Music34.swf',
  35: 'archives:Music35.swf',
  36: 'archives:Music36.swf',
  37: 'archives:Music37.swf',
  38: 'slegacy:media/play/v2/content/global/music/38.swf',
  39: 'slegacy:media/play/v2/content/global/music/39.swf',
  40: 'archives:Music40.swf',
  41: 'archives:Music41.swf',
  42: 'archives:Music42.swf',
  43: 'archives:Music43.swf',
  100: 'archives:Music100.swf',
  101: 'archives:Music101.swf',
  102: 'slegacy:media/play/v2/content/global/music/102.swf',
  103: 'slegacy:media/play/v2/content/global/music/103.swf',
  105: 'slegacy:media/play/v2/content/global/music/105.swf',
  106: 'slegacy:media/play/v2/content/global/music/106.swf',
  110: 'slegacy:media/play/v2/content/global/music/110.swf',
  111: 'slegacy:media/play/v2/content/global/music/111.swf',
  113: 'slegacy:media/play/v2/content/global/music/113.swf',
  114: 'slegacy:media/play/v2/content/global/music/114.swf',
  115: 'slegacy:media/play/v2/content/global/music/115.swf',
  116: 'archives:Music116.swf',
  119: 'slegacy:media/play/v2/content/global/music/119.swf',
  120: 'slegacy:media/play/v2/content/global/music/120.swf',
  121: 'slegacy:media/play/v2/content/global/music/121.swf',
  122: 'archives:Music122.swf',
  123: 'archives:Music123.swf',
  124: 'archives:Music124.swf',
  200: 'archives:Music200.swf',
  201: 'archives:Music201.swf',
  202: 'archives:Music202.swf',
  203: 'archives:Music203.swf',
  204: 'archives:Music204.swf',
  205: 'archives:Music205.swf',
  206: 'archives:Music206.swf',
  208: 'archives:Music208.swf',
  209: 'archives:Music209.swf',
  210: 'archives:Music210.swf',
  211: 'archives:Music211.swf',
  212: 'archives:Music212.swf',
  213: 'archives:Music213.swf',
  214: 'archives:Music214.swf',
  215: 'archives:Music215.swf',
  216: 'archives:Music216.swf',
  217: 'archives:Music217.swf',
  218: 'archives:Music218.swf',
  220: 'archives:Music220.swf',
  221: 'archives:Music221.swf',
  222: 'archives:Music222.swf',
  223: 'archives:Music223.swf',
  224: 'archives:Music224.swf',
  226: 'archives:Music226.swf',
  227: 'archives:Music227.swf',
  228: 'archives:Music228.swf',
  229: 'archives:Music229.swf',
  230: 'archives:Music230.swf',
  231: 'archives:Music231.swf',
  232: 'archives:Music232.swf',
  233: 'archives:Music233.swf',
  234: 'archives:Music234.swf',
  235: 'archives:Music235.swf',
  236: 'archives:Music236.swf',
  237: 'archives:Music237.swf',
  239: 'archives:Music239.swf',
  240: 'archives:Music240.swf',
  241: 'archives:Music241.swf',
  242: 'archives:Music242.swf',
  243: 'archives:Music243.swf',
  244: 'archives:Music244.swf',
  245: 'archives:Music245.swf',
  246: 'archives:Music246.swf',
  247: 'archives:Music247.swf',
  248: 'archives:Music248.swf',
  249: 'archives:Music249.swf',
  250: 'archives:Music250.swf',
  251: 'archives:Music251.swf',
  252: 'archives:Music252.swf',
  253: 'archives:Music253.swf',
  254: 'archives:Music254.swf',
  255: 'archives:Music255.swf',
  256: 'archives:Music256.swf',
  257: 'archives:Music257.swf',
  258: 'archives:Music258.swf',
  259: 'archives:Music259.swf',
  260: 'archives:Music260.swf',
  261: 'archives:Music261.swf',
  262: 'archives:Music262.swf',
  263: 'archives:Music263.swf',
  264: 'archives:Music264.swf',
  265: 'archives:Music265.swf',
  266: 'archives:Music266.swf',
  267: 'archives:Music267.swf',
  268: 'archives:Music268.swf',
  269: 'archives:Music269.swf',
  270: 'archives:Music270.swf',
  271: 'archives:Music271.swf',
  272: 'archives:Music272.swf',
  275: 'archives:Music275.swf',
  276: 'archives:Music276.swf',
  277: 'archives:Music277.swf',
  278: 'archives:Music278.swf',
  279: 'archives:Music279.swf',
  280: 'archives:Music280.swf',
  281: 'archives:Music281.swf',
  282: 'archives:Music282.swf',
  283: 'archives:Music283.swf',
  285: 'archives:Music285.swf',
  286: 'archives:Music286.swf',
  290: 'archives:Music290.swf',
  291: 'archives:Music291.swf',
  293: 'archives:Music293.swf',
  294: 'archives:Music294.swf',
  295: 'archives:Music295.swf',
  297: 'archives:Music297.swf',
  301: 'archives:Music301.swf',
  372: 'archives:Music372.swf',
  400: 'archives:Music400.swf',
  520: 'slegacy:media/play/v2/content/global/music/520.swf',
  532: 'slegacy:media/play/v2/content/global/music/532.swf',
  666: 'slegacy:media/play/v2/content/global/music/666.swf',
  // placeholder ID, we have no idea how these played exactly
  55555: 'archives:MusicSurpriseParty.swf'
};