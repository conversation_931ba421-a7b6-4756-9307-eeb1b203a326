{"dynamic": {"images": [{"name": "iglooedit_icon", "originX": 89, "originY": 35, "width": 47, "height": 49, "image": "catalog/iglooedit/iglooedit_icon.png"}]}, "layout": {"labels": [{"color": "#6a4e27", "originX": 133, "originY": 43, "width": 273.8, "size": "20", "name": "iglooedit_text", "token": "iglooedit.catalog_header.iglooedit_text", "align": "center", "height": 29.85, "font": "cpBurbankSmallBold"}], "frames": [{"name": "iglooedit", "originX": 15, "originY": 81, "width": 993, "height": 618, "configurator_id": 0}]}, "sourceWidth": 1024, "sourceHeight": 768, "configurator": [{"is_member": "true", "prompt": "Black TV Stand", "category": "furniture_item", "id": "2347", "label": "Black TV Stand", "cost": "400", "type": "1"}, {"is_member": "true", "prompt": "Gray TV Stand", "category": "furniture_item", "id": "2348", "label": "Gray TV Stand", "cost": "400", "type": "1"}, {"is_member": "true", "prompt": "Black Designer Couch", "category": "furniture_item", "id": "2349", "label": "Black Designer Couch", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Brown Designer Couch", "category": "furniture_item", "id": "2350", "label": "Brown Designer Couch", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Red Designer Couch", "category": "furniture_item", "id": "2351", "label": "Red Designer Couch", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Multi Shelves", "category": "furniture_item", "id": "2352", "label": "Multi Shelves", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Wood Shelves", "category": "furniture_item", "id": "2353", "label": "Wood Shelves", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "CP Air Seat", "category": "furniture_item", "id": "2202", "label": "CP Air Seat", "cost": "75", "type": "1"}, {"is_member": "true", "prompt": "CP Air Pilot's Chair", "category": "furniture_item", "id": "2203", "label": "CP Air Pilot's Chair", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Check-in Terminal", "category": "furniture_item", "id": "2204", "label": "Check-in Terminal", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Gate Chair", "category": "furniture_item", "id": "2205", "label": "Gate Chair", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Airport Departures Board", "category": "furniture_item", "id": "2206", "label": "Airport Departures Board", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Elastic Barrier", "category": "furniture_item", "id": "2207", "label": "Elastic Barrier", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Construction Pylon", "category": "furniture_item", "id": "562", "label": "Construction Pylon", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Construction Sign", "category": "furniture_item", "id": "564", "label": "Construction Sign", "cost": "145", "type": "1"}, {"is_member": "true", "prompt": "Newspaper Stand", "category": "furniture_item", "id": "865", "label": "Newspaper Stand", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Footlight Stage", "category": "furniture_item", "id": "2211", "label": "Footlight Stage", "cost": "75", "type": "1"}, {"is_member": "true", "prompt": "Food Stand", "category": "furniture_item", "id": "757", "label": "Food Stand", "cost": "550", "type": "1"}, {"is_member": "true", "prompt": "Balloon Bunch", "category": "furniture_item", "id": "749", "label": "Balloon Bunch", "cost": "125", "type": "1"}, {"is_member": "true", "prompt": "Patio Parasol", "category": "furniture_item", "id": "2280", "label": "Patio Parasol", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Popcorn Cart", "category": "furniture_item", "id": "2189", "label": "Popcorn Cart", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Beach Party Ball", "category": "furniture_item", "id": "2277", "label": "Beach Party Ball", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Stone Column R<PERSON>s", "category": "furniture_item", "id": "548", "label": "Stone Column R<PERSON>s", "cost": "350", "type": "1"}, {"is_member": "true", "prompt": "Sea Stones", "category": "furniture_item", "id": "808", "label": "Sea Stones", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Pirate Barrel", "category": "furniture_item", "id": "2303", "label": "Pirate Barrel", "cost": "75", "type": "1"}, {"is_member": "true", "prompt": "Ship's Wheel", "category": "furniture_item", "id": "2316", "label": "Ship's Wheel", "cost": "75", "type": "1"}, {"is_member": "true", "prompt": "<PERSON><PERSON> Chair", "category": "furniture_item", "id": "2187", "label": "<PERSON><PERSON> Chair", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Wooden Crate", "category": "furniture_item", "id": "165", "label": "Wooden Crate", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "In Half Igloo", "category": "igloo", "id": "37", "label": "In Half Igloo", "cost": "2300"}, {"is_member": "true", "prompt": "Tent", "category": "igloo", "id": "19", "label": "Tent", "cost": "2700"}, {"is_member": "true", "prompt": "Log Cabin", "category": "igloo", "id": "11", "label": "Log Cabin", "cost": "4100"}, {"is_member": "true", "prompt": "Ship Igloo", "category": "igloo", "id": "23", "label": "Ship Igloo", "cost": "4300"}, {"is_member": "true", "prompt": "Dance Floor", "category": "igloo_floor", "id": "7", "label": "Dance Floor", "cost": "1000"}, {"is_member": "true", "prompt": "Blue Turf", "category": "igloo_floor", "id": "11", "label": "Blue Turf", "cost": "530"}, {"is_member": "true", "prompt": "Phony Lawn", "category": "igloo_floor", "id": "14", "label": "Phony Lawn", "cost": "700"}, {"is_member": "true", "prompt": "Black Carpet", "category": "igloo_floor", "id": "15", "label": "Black Carpet", "cost": "530"}, {"is_member": "true", "prompt": "Sunny Sky Floor", "category": "igloo_floor", "id": "19", "label": "Sunny Sky Floor", "cost": "530"}, {"is_member": "true", "prompt": "Snowy Floor", "category": "igloo_floor", "id": "21", "label": "Snowy Floor", "cost": "400"}, {"is_member": "true", "prompt": "Lime Green Carpet", "category": "igloo_floor", "id": "22", "label": "Lime Green Carpet", "cost": "530"}, {"is_member": "true", "prompt": "Pink Carpet", "category": "igloo_floor", "id": "17", "label": "Pink Carpet", "cost": "530"}, {"is_member": "false", "prompt": "<PERSON><PERSON>", "category": "furniture_item", "id": "788", "label": "<PERSON><PERSON>", "cost": "200", "type": "1"}, {"is_member": "false", "prompt": "Arm Chair", "category": "furniture_item", "id": "787", "label": "Arm Chair", "cost": "200", "type": "1"}, {"is_member": "false", "prompt": "Mini Pumpkin Lanterns", "category": "furniture_item", "id": "2097", "label": "Mini Pumpkin Lanterns", "cost": "20", "type": "2"}, {"is_member": "false", "prompt": "Door Mat", "category": "furniture_item", "id": "792", "label": "Door Mat", "cost": "200", "type": "1"}, {"is_member": "false", "prompt": "Fern", "category": "furniture_item", "id": "790", "label": "Fern", "cost": "200", "type": "1"}, {"is_member": "false", "prompt": "Potted Poinsettia", "category": "furniture_item", "id": "2321", "label": "Potted Poinsettia", "cost": "100", "type": "1"}, {"is_member": "false", "prompt": "Green Clover", "category": "furniture_item", "id": "609", "label": "Green Clover", "cost": "30", "type": "2"}, {"is_member": "false", "prompt": "Inspiration Station", "category": "furniture_item", "id": "2201", "label": "Inspiration Station", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Mounted Speaker", "category": "furniture_item", "id": "2354", "label": "Mounted Speaker", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Starship Panel", "category": "furniture_item", "id": "2331", "label": "Starship Panel", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "<PERSON>", "category": "furniture_item", "id": "2325", "label": "<PERSON>", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "Ice Cube Wreath", "category": "furniture_item", "id": "2318", "label": "Ice Cube Wreath", "cost": "75", "type": "2"}, {"is_member": "true", "prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> Curtains", "category": "furniture_item", "id": "2296", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> Curtains", "cost": "150", "type": "2"}, {"is_member": "true", "prompt": "Corner Wall Cabinet", "category": "furniture_item", "id": "2262", "label": "Corner Wall Cabinet", "cost": "75", "type": "2"}, {"is_member": "true", "prompt": "Double Wall Cabinet", "category": "furniture_item", "id": "2261", "label": "Double Wall Cabinet", "cost": "75", "type": "2"}, {"is_member": "true", "prompt": "Wall Cabinet", "category": "furniture_item", "id": "2260", "label": "Wall Cabinet", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Upper Cabinets", "category": "furniture_item", "id": "2257", "label": "Upper Cabinets", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "Go Sharks", "category": "furniture_item", "id": "2243", "label": "Go Sharks", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Go Space Squids", "category": "furniture_item", "id": "2244", "label": "Go Space Squids", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Go Hot Sauce", "category": "furniture_item", "id": "2245", "label": "Go Hot Sauce", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "<PERSON> Flu<PERSON>ies", "category": "furniture_item", "id": "2246", "label": "<PERSON> Flu<PERSON>ies", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Fish Fossil", "category": "furniture_item", "id": "2173", "label": "Fish Fossil", "cost": "150", "type": "2"}, {"is_member": "true", "prompt": "<PERSON><PERSON><PERSON>", "category": "furniture_item", "id": "2104", "label": "<PERSON><PERSON><PERSON>", "cost": "150", "type": "2"}, {"is_member": "true", "prompt": "<PERSON>", "category": "furniture_item", "id": "2103", "label": "<PERSON>", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Scary Lantern Light", "category": "furniture_item", "id": "2102", "label": "Scary Lantern Light", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Window", "category": "furniture_item", "id": "560", "label": "Window", "cost": "600", "type": "2"}, {"is_member": "true", "prompt": "Neon Bat", "category": "furniture_item", "id": "2100", "label": "Neon Bat", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "<PERSON>", "category": "furniture_item", "id": "2099", "label": "<PERSON>", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "<PERSON>", "category": "furniture_item", "id": "2096", "label": "<PERSON>", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "furniture_item", "id": "2095", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "<PERSON>", "category": "furniture_item", "id": "2094", "label": "<PERSON>", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Garland of Sweets", "category": "furniture_item", "id": "2093", "label": "Garland of Sweets", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Ghastly Window", "category": "furniture_item", "id": "2092", "label": "Ghastly Window", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "Ogre Puffle Head", "category": "furniture_item", "id": "2086", "label": "Ogre Puffle Head", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "<PERSON><PERSON><PERSON>", "category": "furniture_item", "id": "2072", "label": "<PERSON><PERSON><PERSON>", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Regal Dr<PERSON>", "category": "furniture_item", "id": "2070", "label": "Regal Dr<PERSON>", "cost": "200", "type": "2"}, {"is_member": "true", "prompt": "Pink Curtains", "category": "furniture_item", "id": "2057", "label": "Pink Curtains", "cost": "200", "type": "2"}, {"is_member": "true", "prompt": "Death Star", "category": "furniture_item", "id": "2045", "label": "Death Star", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "3D ROR", "category": "furniture_item", "id": "2022", "label": "3D ROR", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "ROR Pennant", "category": "furniture_item", "id": "2021", "label": "ROR Pennant", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Red Triangle Pennants", "category": "furniture_item", "id": "2020", "label": "Red Triangle Pennants", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "3D JOX", "category": "furniture_item", "id": "2011", "label": "3D JOX", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "JOX Pennant", "category": "furniture_item", "id": "2010", "label": "JOX Pennant", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Holiday Bells", "category": "furniture_item", "id": "590", "label": "Holiday Bells", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "3D PNK", "category": "furniture_item", "id": "2009", "label": "3D PNK", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "PNK <PERSON>", "category": "furniture_item", "id": "2008", "label": "PNK <PERSON>", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "3D OK", "category": "furniture_item", "id": "2007", "label": "3D OK", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "OK Pennant", "category": "furniture_item", "id": "2006", "label": "OK Pennant", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Monster Scoreboard", "category": "furniture_item", "id": "2004", "label": "Monster Scoreboard", "cost": "250", "type": "2"}, {"is_member": "true", "prompt": "Pink Triangle Pennants", "category": "furniture_item", "id": "2002", "label": "Pink Triangle Pennants", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Green Triangle Pennants", "category": "furniture_item", "id": "2001", "label": "Green Triangle Pennants", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Floral Paper Screen", "category": "furniture_item", "id": "992", "label": "Floral Paper Screen", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "Orange Triangle Pennants", "category": "furniture_item", "id": "2003", "label": "Orange Triangle Pennants", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Earth", "category": "furniture_item", "id": "967", "label": "Earth", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "Mars", "category": "furniture_item", "id": "966", "label": "Mars", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "Leafy Window", "category": "furniture_item", "id": "946", "label": "Leafy Window", "cost": "700", "type": "2"}, {"is_member": "true", "prompt": "Full Moon", "category": "furniture_item", "id": "908", "label": "Full Moon", "cost": "250", "type": "2"}, {"is_member": "true", "prompt": "Thatched Awning", "category": "furniture_item", "id": "884", "label": "Thatched Awning", "cost": "250", "type": "2"}, {"is_member": "true", "prompt": "Show Lights", "category": "furniture_item", "id": "872", "label": "Show Lights", "cost": "500", "type": "2"}, {"is_member": "true", "prompt": "Cityscape", "category": "furniture_item", "id": "861", "label": "Cityscape", "cost": "550", "type": "2"}, {"is_member": "true", "prompt": "Dragon Flag", "category": "furniture_item", "id": "857", "label": "Dragon Flag", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Concert Lights", "category": "furniture_item", "id": "355", "label": "Concert Lights", "cost": "650", "type": "2"}, {"is_member": "true", "prompt": "Sky Flag", "category": "furniture_item", "id": "856", "label": "Sky Flag", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Scorn Flag", "category": "furniture_item", "id": "855", "label": "Scorn Flag", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Village Flag", "category": "furniture_item", "id": "854", "label": "Village Flag", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Wizard Flag", "category": "furniture_item", "id": "853", "label": "Wizard Flag", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Fairy Woods Flag", "category": "furniture_item", "id": "852", "label": "Fairy Woods Flag", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Kingdom Flag", "category": "furniture_item", "id": "851", "label": "Kingdom Flag", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Wispy Clouds", "category": "furniture_item", "id": "848", "label": "Wispy Clouds", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "Fun Fungus", "category": "furniture_item", "id": "847", "label": "Fun Fungus", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "Cheeky Lantern", "category": "furniture_item", "id": "829", "label": "Cheeky Lantern", "cost": "175", "type": "2"}, {"is_member": "true", "prompt": "Grumpy Lantern", "category": "furniture_item", "id": "828", "label": "Grumpy Lantern", "cost": "175", "type": "2"}, {"is_member": "true", "prompt": "Laughing Lantern", "category": "furniture_item", "id": "827", "label": "Laughing Lantern", "cost": "175", "type": "2"}, {"is_member": "true", "prompt": "Happy Lantern", "category": "furniture_item", "id": "826", "label": "Happy Lantern", "cost": "175", "type": "2"}, {"is_member": "true", "prompt": "Cozy Fireplace", "category": "furniture_item", "id": "804", "label": "Cozy Fireplace", "cost": "700", "type": "2"}, {"is_member": "true", "prompt": "Holiday Tree Decoration", "category": "furniture_item", "id": "802", "label": "Holiday Tree Decoration", "cost": "150", "type": "2"}, {"is_member": "true", "prompt": "Holiday Star Decoration", "category": "furniture_item", "id": "801", "label": "Holiday Star Decoration", "cost": "150", "type": "2"}, {"is_member": "true", "prompt": "Icing Decorations", "category": "furniture_item", "id": "799", "label": "Icing Decorations", "cost": "30", "type": "2"}, {"is_member": "true", "prompt": "<PERSON><PERSON><PERSON>", "category": "furniture_item", "id": "796", "label": "<PERSON><PERSON><PERSON>", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Ornate Mirror", "category": "furniture_item", "id": "536", "label": "Ornate Mirror", "cost": "680", "type": "2"}, {"is_member": "true", "prompt": "Purple Paper Lantern", "category": "furniture_item", "id": "785", "label": "Purple Paper Lantern", "cost": "30", "type": "2"}, {"is_member": "true", "prompt": "Yellow Paper Lantern", "category": "furniture_item", "id": "784", "label": "Yellow Paper Lantern", "cost": "30", "type": "2"}, {"is_member": "true", "prompt": "Red Paper Lantern", "category": "furniture_item", "id": "783", "label": "Red Paper Lantern", "cost": "30", "type": "2"}, {"is_member": "true", "prompt": "Blue Paper Lantern", "category": "furniture_item", "id": "782", "label": "Blue Paper Lantern", "cost": "30", "type": "2"}, {"is_member": "true", "prompt": "Green Paper Lantern", "category": "furniture_item", "id": "775", "label": "Green Paper Lantern", "cost": "30", "type": "2"}, {"is_member": "true", "prompt": "Wall Bats", "category": "furniture_item", "id": "771", "label": "Wall Bats", "cost": "75", "type": "2"}, {"is_member": "true", "prompt": "Wall Ghosts", "category": "furniture_item", "id": "770", "label": "Wall Ghosts", "cost": "75", "type": "2"}, {"is_member": "true", "prompt": "<PERSON>", "category": "furniture_item", "id": "769", "label": "<PERSON>", "cost": "75", "type": "2"}, {"is_member": "true", "prompt": "<PERSON><PERSON>", "category": "furniture_item", "id": "768", "label": "<PERSON><PERSON>", "cost": "75", "type": "2"}, {"is_member": "true", "prompt": "Terrifying Tissue Ghost", "category": "furniture_item", "id": "767", "label": "Terrifying Tissue Ghost", "cost": "150", "type": "2"}, {"is_member": "true", "prompt": "Jack-O-<PERSON>", "category": "furniture_item", "id": "766", "label": "Jack-O-<PERSON>", "cost": "100", "type": "2"}, {"is_member": "true", "prompt": "Snappy <PERSON>", "category": "furniture_item", "id": "729", "label": "Snappy <PERSON>", "cost": "250", "type": "2"}, {"is_member": "true", "prompt": "Swinging Vines", "category": "furniture_item", "id": "722", "label": "Swinging Vines", "cost": "200", "type": "2"}, {"is_member": "true", "prompt": "Sea Streamers", "category": "furniture_item", "id": "721", "label": "Sea Streamers", "cost": "45", "type": "2"}, {"is_member": "true", "prompt": "Hanging Algae", "category": "furniture_item", "id": "720", "label": "Hanging Algae", "cost": "30", "type": "2"}, {"is_member": "true", "prompt": "Laser Lights", "category": "furniture_item", "id": "719", "label": "Laser Lights", "cost": "145", "type": "2"}, {"is_member": "true", "prompt": "Electric Encore", "category": "furniture_item", "id": "718", "label": "Electric Encore", "cost": "175", "type": "2"}, {"is_member": "true", "prompt": "Musical Motif", "category": "furniture_item", "id": "717", "label": "Musical Motif", "cost": "225", "type": "2"}, {"is_member": "true", "prompt": "<PERSON><PERSON><PERSON> (guit", "category": "furniture_item", "id": "716", "label": "<PERSON><PERSON><PERSON> (guit", "cost": "175", "type": "2"}, {"is_member": "true", "prompt": "Hanging Moss", "category": "furniture_item", "id": "700", "label": "Hanging Moss", "cost": "150", "type": "2"}, {"is_member": "true", "prompt": "Vines", "category": "furniture_item", "id": "699", "label": "Vines", "cost": "200", "type": "2"}, {"is_member": "true", "prompt": "Ye Olde Blue Banner", "category": "furniture_item", "id": "697", "label": "Ye Olde Blue Banner", "cost": "200", "type": "2"}, {"is_member": "true", "prompt": "Ye Olde Red Banner", "category": "furniture_item", "id": "696", "label": "Ye Olde Red Banner", "cost": "200", "type": "2"}, {"is_member": "true", "prompt": "Ye Olde Yellow Banne", "category": "furniture_item", "id": "698", "label": "Ye Olde Yellow Banne", "cost": "200", "type": "2"}, {"is_member": "true", "prompt": "Island Trinkets", "category": "furniture_item", "id": "703", "label": "Island Trinkets", "cost": "250", "type": "2"}, {"is_member": "true", "prompt": "Dream Catcher", "category": "furniture_item", "id": "662", "label": "Dream Catcher", "cost": "250", "type": "2"}, {"is_member": "true", "prompt": "Orange Puffle Pictur", "category": "furniture_item", "id": "690", "label": "Orange Puffle Pictur", "cost": "80", "type": "2"}, {"is_member": "true", "prompt": "<PERSON> P<PERSON>le <PERSON>", "category": "furniture_item", "id": "674", "label": "<PERSON> P<PERSON>le <PERSON>", "cost": "80", "type": "2"}, {"is_member": "true", "prompt": "White Puffle Picture", "category": "furniture_item", "id": "673", "label": "White Puffle Picture", "cost": "80", "type": "2"}, {"is_member": "true", "prompt": "Yellow Puffle Pictur", "category": "furniture_item", "id": "672", "label": "Yellow Puffle Pictur", "cost": "80", "type": "2"}, {"is_member": "true", "prompt": "Purple Puffle Pictur", "category": "furniture_item", "id": "671", "label": "Purple Puffle Pictur", "cost": "80", "type": "2"}, {"is_member": "true", "prompt": "Pink Puffle Picture", "category": "furniture_item", "id": "670", "label": "Pink Puffle Picture", "cost": "80", "type": "2"}, {"is_member": "true", "prompt": "Green Puffle Picture", "category": "furniture_item", "id": "669", "label": "Green Puffle Picture", "cost": "80", "type": "2"}, {"is_member": "true", "prompt": "Red Puffle Picture", "category": "furniture_item", "id": "668", "label": "Red Puffle Picture", "cost": "80", "type": "2"}, {"is_member": "true", "prompt": "Blue Puffle Picture", "category": "furniture_item", "id": "667", "label": "Blue Puffle Picture", "cost": "80", "type": "2"}, {"is_member": "true", "prompt": "<PERSON> Puffle Picture", "category": "furniture_item", "id": "666", "label": "<PERSON> Puffle Picture", "cost": "80", "type": "2"}, {"is_member": "true", "prompt": "Confetti Blaster", "category": "furniture_item", "id": "710", "label": "Confetti Blaster", "cost": "25", "type": "2"}, {"is_member": "true", "prompt": "Hanging Basket", "category": "furniture_item", "id": "688", "label": "Hanging Basket", "cost": "250", "type": "2"}, {"is_member": "true", "prompt": "Window Basket", "category": "furniture_item", "id": "685", "label": "Window Basket", "cost": "300", "type": "2"}, {"is_member": "true", "prompt": "Stockings", "category": "furniture_item", "id": "659", "label": "Stockings", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Snowflake", "category": "furniture_item", "id": "657", "label": "Snowflake", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "Iron Chandelier", "category": "furniture_item", "id": "653", "label": "Iron Chandelier", "cost": "600", "type": "2"}, {"is_member": "true", "prompt": "Multi-pane Window", "category": "furniture_item", "id": "652", "label": "Multi-pane Window", "cost": "250", "type": "2"}, {"is_member": "true", "prompt": "Perched P<PERSON>le <PERSON>atu", "category": "furniture_item", "id": "643", "label": "Perched P<PERSON>le <PERSON>atu", "cost": "275", "type": "2"}, {"is_member": "true", "prompt": "Yellow CP Banner", "category": "furniture_item", "id": "642", "label": "Yellow CP Banner", "cost": "170", "type": "2"}, {"is_member": "true", "prompt": "White Board", "category": "furniture_item", "id": "641", "label": "White Board", "cost": "400", "type": "2"}, {"is_member": "true", "prompt": "Scroll-down Map", "category": "furniture_item", "id": "640", "label": "Scroll-down Map", "cost": "150", "type": "2"}, {"is_member": "true", "prompt": "Classroom Bell", "category": "furniture_item", "id": "639", "label": "Classroom Bell", "cost": "75", "type": "2"}, {"is_member": "true", "prompt": "<PERSON>", "category": "furniture_item", "id": "632", "label": "<PERSON>", "cost": "350", "type": "2"}, {"is_member": "true", "prompt": "Curtains Burgundy", "category": "furniture_item", "id": "624", "label": "Curtains Burgundy", "cost": "350", "type": "2"}, {"is_member": "true", "prompt": "Rainbow w Pot o Gold", "category": "furniture_item", "id": "616", "label": "Rainbow w Pot o Gold", "cost": "450", "type": "2"}, {"is_member": "true", "prompt": "Clover Garland", "category": "furniture_item", "id": "611", "label": "Clover Garland", "cost": "150", "type": "2"}, {"is_member": "true", "prompt": "HD TV", "category": "furniture_item", "id": "596", "label": "HD TV", "cost": "1000", "type": "2"}, {"is_member": "true", "prompt": "<PERSON><PERSON>", "category": "furniture_item", "id": "2098", "label": "<PERSON><PERSON>", "cost": "50", "type": "2"}, {"is_member": "true", "prompt": "River's Edge", "category": "furniture_item", "id": "2138", "label": "River's Edge", "cost": "150", "type": "3"}, {"is_member": "true", "prompt": "Purple Pit", "category": "furniture_item", "id": "2108", "label": "Purple Pit", "cost": "200", "type": "3"}, {"is_member": "true", "prompt": "Magical Garden", "category": "furniture_item", "id": "2080", "label": "Magical Garden", "cost": "50", "type": "3"}, {"is_member": "true", "prompt": "Imperial House Rug", "category": "furniture_item", "id": "2037", "label": "Imperial House Rug", "cost": "150", "type": "3"}, {"is_member": "true", "prompt": "Rebel House Rug", "category": "furniture_item", "id": "2036", "label": "Rebel House Rug", "cost": "150", "type": "3"}, {"is_member": "true", "prompt": "Blue Floor Cushion", "category": "furniture_item", "id": "997", "label": "Blue Floor Cushion", "cost": "50", "type": "3"}, {"is_member": "true", "prompt": "Rock Garden", "category": "furniture_item", "id": "996", "label": "Rock Garden", "cost": "300", "type": "3"}, {"is_member": "true", "prompt": "Stone Walk Way", "category": "furniture_item", "id": "993", "label": "Stone Walk Way", "cost": "150", "type": "3"}, {"is_member": "true", "prompt": "Puffle Stage", "category": "furniture_item", "id": "980", "label": "Puffle Stage", "cost": "300", "type": "3"}, {"is_member": "true", "prompt": "Rainbow Bridge", "category": "furniture_item", "id": "976", "label": "Rainbow Bridge", "cost": "400", "type": "3"}, {"is_member": "true", "prompt": "Skating Rink", "category": "furniture_item", "id": "975", "label": "Skating Rink", "cost": "400", "type": "3"}, {"is_member": "true", "prompt": "Basketball Court", "category": "furniture_item", "id": "960", "label": "Basketball Court", "cost": "300", "type": "3"}, {"is_member": "true", "prompt": "<PERSON>", "category": "furniture_item", "id": "924", "label": "<PERSON>", "cost": "250", "type": "3"}, {"is_member": "true", "prompt": "Ectoplasmic Crevasse", "category": "furniture_item", "id": "911", "label": "Ectoplasmic Crevasse", "cost": "450", "type": "3"}, {"is_member": "true", "prompt": "Manhole", "category": "furniture_item", "id": "863", "label": "Manhole", "cost": "100", "type": "3"}, {"is_member": "true", "prompt": "Watering Hole", "category": "furniture_item", "id": "837", "label": "Watering Hole", "cost": "500", "type": "3"}, {"is_member": "true", "prompt": "Surf <PERSON> Towel", "category": "furniture_item", "id": "183", "label": "Surf <PERSON> Towel", "cost": "250", "type": "3"}, {"is_member": "true", "prompt": "Monster Rug", "category": "furniture_item", "id": "2016", "label": "Monster Rug", "cost": "300", "type": "3"}, {"is_member": "true", "prompt": "<PERSON><PERSON><PERSON> Rug", "category": "furniture_item", "id": "830", "label": "<PERSON><PERSON><PERSON> Rug", "cost": "200", "type": "3"}, {"is_member": "true", "prompt": "Card-Jitsu Mat", "category": "furniture_item", "id": "786", "label": "Card-Jitsu Mat", "cost": "700", "type": "3"}, {"is_member": "true", "prompt": "Swamp Slime", "category": "furniture_item", "id": "773", "label": "Swamp Slime", "cost": "230", "type": "3"}, {"is_member": "true", "prompt": "Weathered Path", "category": "furniture_item", "id": "745", "label": "Weathered Path", "cost": "120", "type": "3"}, {"is_member": "true", "prompt": "<PERSON><PERSON>", "category": "furniture_item", "id": "743", "label": "<PERSON><PERSON>", "cost": "350", "type": "3"}, {"is_member": "true", "prompt": "Half-pipe", "category": "furniture_item", "id": "739", "label": "Half-pipe", "cost": "400", "type": "3"}, {"is_member": "true", "prompt": "Dance Mat", "category": "furniture_item", "id": "713", "label": "Dance Mat", "cost": "135", "type": "3"}, {"is_member": "true", "prompt": "Trap Door", "category": "furniture_item", "id": "692", "label": "Trap Door", "cost": "250", "type": "3"}, {"is_member": "true", "prompt": "<PERSON>n <PERSON>", "category": "furniture_item", "id": "647", "label": "<PERSON>n <PERSON>", "cost": "100", "type": "3"}, {"is_member": "true", "prompt": "<PERSON><PERSON>", "category": "furniture_item", "id": "646", "label": "<PERSON><PERSON>", "cost": "50", "type": "3"}, {"is_member": "true", "prompt": "Ice Fishing Decal", "category": "furniture_item", "id": "607", "label": "Ice Fishing Decal", "cost": "50", "type": "3"}, {"is_member": "true", "prompt": "<PERSON><PERSON>", "category": "furniture_item", "id": "606", "label": "<PERSON><PERSON>", "cost": "500", "type": "3"}, {"is_member": "true", "prompt": "Puzzle Floor", "category": "furniture_item", "id": "571", "label": "Puzzle Floor", "cost": "200", "type": "3"}, {"is_member": "true", "prompt": "Bowling Alley", "category": "furniture_item", "id": "565", "label": "Bowling Alley", "cost": "700", "type": "3"}, {"is_member": "true", "prompt": "Celtic Rug", "category": "furniture_item", "id": "535", "label": "Celtic Rug", "cost": "550", "type": "3"}, {"is_member": "true", "prompt": "Tennis Court", "category": "furniture_item", "id": "522", "label": "Tennis Court", "cost": "700", "type": "3"}, {"is_member": "true", "prompt": "Pitchers Mound", "category": "furniture_item", "id": "521", "label": "Pitchers Mound", "cost": "175", "type": "3"}, {"is_member": "true", "prompt": "Rug Map Area", "category": "furniture_item", "id": "494", "label": "Rug Map Area", "cost": "450", "type": "3"}, {"is_member": "true", "prompt": "Modern Coffee Table", "category": "furniture_item", "id": "2248", "label": "Modern Coffee Table", "cost": "75", "type": "1"}, {"is_member": "true", "prompt": "Modern End Table", "category": "furniture_item", "id": "2249", "label": "Modern End Table", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Granite Top Dishwasher", "category": "furniture_item", "id": "2250", "label": "Granite Top Dishwasher", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Granite Top Corner Cabinet", "category": "furniture_item", "id": "2251", "label": "Granite Top Corner Cabinet", "cost": "75", "type": "1"}, {"is_member": "true", "prompt": "Granite Top Cabinet", "category": "furniture_item", "id": "2252", "label": "Granite Top Cabinet", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Granite Sink", "category": "furniture_item", "id": "2253", "label": "Granite Sink", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Granite Top Double Cabinet", "category": "furniture_item", "id": "2254", "label": "Granite Top Double Cabinet", "cost": "75", "type": "1"}, {"is_member": "true", "prompt": "Brushed Steel Fridge", "category": "furniture_item", "id": "2255", "label": "Brushed Steel Fridge", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Granite Kitchen Island", "category": "furniture_item", "id": "2256", "label": "Granite Kitchen Island", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Brushed Steel Oven", "category": "furniture_item", "id": "2258", "label": "Brushed Steel Oven", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Kitchen Stool", "category": "furniture_item", "id": "2259", "label": "Kitchen Stool", "cost": "75", "type": "1"}, {"is_member": "true", "prompt": "Cardboard Herbert", "category": "furniture_item", "id": "917", "label": "Cardboard Herbert", "cost": "600", "type": "1"}, {"is_member": "true", "prompt": "Safe", "category": "furniture_item", "id": "918", "label": "Safe", "cost": "450", "type": "1"}, {"is_member": "true", "prompt": "Containment Cell", "category": "furniture_item", "id": "919", "label": "Containment Cell", "cost": "700", "type": "1"}, {"is_member": "true", "prompt": "Emergency Light", "category": "furniture_item", "id": "920", "label": "Emergency Light", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Paw Print", "category": "furniture_item", "id": "921", "label": "Paw Print", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Spy Car", "category": "furniture_item", "id": "922", "label": "Spy Car", "cost": "900", "type": "1"}, {"is_member": "true", "prompt": "<PERSON>", "category": "furniture_item", "id": "925", "label": "<PERSON>", "cost": "475", "type": "1"}, {"is_member": "true", "prompt": "<PERSON>hrone", "category": "furniture_item", "id": "926", "label": "<PERSON>hrone", "cost": "750", "type": "1"}, {"is_member": "true", "prompt": "Cozy Red House", "category": "furniture_item", "id": "930", "label": "Cozy Red House", "cost": "800", "type": "1"}, {"is_member": "true", "prompt": "Cozy Blue Door", "category": "furniture_item", "id": "931", "label": "Cozy Blue Door", "cost": "800", "type": "1"}, {"is_member": "true", "prompt": "Tinker Train Engine", "category": "furniture_item", "id": "932", "label": "Tinker Train Engine", "cost": "700", "type": "1"}, {"is_member": "true", "prompt": "Tinker Train Car", "category": "furniture_item", "id": "933", "label": "Tinker Train Car", "cost": "350", "type": "1"}, {"is_member": "true", "prompt": "Holiday Fireplace", "category": "furniture_item", "id": "935", "label": "Holiday Fireplace", "cost": "500", "type": "1"}, {"is_member": "true", "prompt": "Hollow Tree", "category": "furniture_item", "id": "937", "label": "Hollow Tree", "cost": "500", "type": "1"}, {"is_member": "true", "prompt": "Trusty Post", "category": "furniture_item", "id": "938", "label": "Trusty Post", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Wooden Walk", "category": "furniture_item", "id": "939", "label": "Wooden Walk", "cost": "400", "type": "1"}, {"is_member": "true", "prompt": "Trustier Post", "category": "furniture_item", "id": "940", "label": "Trustier Post", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "<PERSON><PERSON>", "category": "furniture_item", "id": "941", "label": "<PERSON><PERSON>", "cost": "550", "type": "1"}, {"is_member": "true", "prompt": "<PERSON><PERSON>", "category": "furniture_item", "id": "942", "label": "<PERSON><PERSON>", "cost": "350", "type": "1"}, {"is_member": "true", "prompt": "Lava Pool", "category": "furniture_item", "id": "943", "label": "Lava Pool", "cost": "550", "type": "1"}, {"is_member": "true", "prompt": "Wooden Steps", "category": "furniture_item", "id": "944", "label": "Wooden Steps", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Short Wooden Steps", "category": "furniture_item", "id": "945", "label": "Short Wooden Steps", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Tall Grass", "category": "furniture_item", "id": "947", "label": "Tall Grass", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "<PERSON><PERSON>", "category": "furniture_item", "id": "936", "label": "<PERSON><PERSON>", "cost": "800", "type": "1"}, {"is_member": "true", "prompt": "Cozy Green House", "category": "furniture_item", "id": "929", "label": "Cozy Green House", "cost": "800", "type": "1"}, {"is_member": "true", "prompt": "<PERSON><PERSON>", "category": "furniture_item", "id": "948", "label": "<PERSON><PERSON>", "cost": "550", "type": "1"}, {"is_member": "true", "prompt": "Comfy Stump", "category": "furniture_item", "id": "949", "label": "Comfy Stump", "cost": "500", "type": "1"}, {"is_member": "true", "prompt": "Space Age Lights", "category": "furniture_item", "id": "959", "label": "Space Age Lights", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Grand Piano", "category": "furniture_item", "id": "961", "label": "Grand Piano", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "DJ <PERSON>", "category": "furniture_item", "id": "962", "label": "DJ <PERSON>", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Jumbo Remote", "category": "furniture_item", "id": "963", "label": "Jumbo Remote", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Diner Counter", "category": "furniture_item", "id": "964", "label": "Diner Counter", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Jumbo TV", "category": "furniture_item", "id": "965", "label": "Jumbo TV", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Basketball Hoop", "category": "furniture_item", "id": "968", "label": "Basketball Hoop", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Spaceship", "category": "furniture_item", "id": "970", "label": "Spaceship", "cost": "600", "type": "1"}, {"is_member": "true", "prompt": "Puffle Carrier", "category": "furniture_item", "id": "973", "label": "Puffle Carrier", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Arcade Game", "category": "furniture_item", "id": "974", "label": "Arcade Game", "cost": "350", "type": "1"}, {"is_member": "true", "prompt": "Puffle Shop Shelf", "category": "furniture_item", "id": "977", "label": "Puffle Shop Shelf", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Puffle Shop Till", "category": "furniture_item", "id": "978", "label": "Puffle Shop Till", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Unicycle Tightrope", "category": "furniture_item", "id": "979", "label": "Unicycle Tightrope", "cost": "250", "type": "1"}, {"is_member": "true", "prompt": "Clinic Entrance", "category": "furniture_item", "id": "984", "label": "Clinic Entrance", "cost": "800", "type": "1"}, {"is_member": "true", "prompt": "General Store Front", "category": "furniture_item", "id": "985", "label": "General Store Front", "cost": "800", "type": "1"}, {"is_member": "true", "prompt": "X-Ray Machine", "category": "furniture_item", "id": "986", "label": "X-Ray Machine", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Hospital Chair", "category": "furniture_item", "id": "987", "label": "Hospital Chair", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Operating Room Lights", "category": "furniture_item", "id": "988", "label": "Operating Room Lights", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Deluxe Tool Chest", "category": "furniture_item", "id": "989", "label": "Deluxe Tool Chest", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Sword Display", "category": "furniture_item", "id": "999", "label": "Sword Display", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Monster Bleachers", "category": "furniture_item", "id": "2000", "label": "Monster Bleachers", "cost": "250", "type": "1"}, {"is_member": "true", "prompt": "Monster Archway", "category": "furniture_item", "id": "2005", "label": "Monster Archway", "cost": "400", "type": "1"}, {"is_member": "true", "prompt": "Monster Table", "category": "furniture_item", "id": "2012", "label": "Monster Table", "cost": "250", "type": "1"}, {"is_member": "true", "prompt": "Monster Library Shelves", "category": "furniture_item", "id": "2013", "label": "Monster Library Shelves", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Scare Can", "category": "furniture_item", "id": "2014", "label": "Scare Can", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Monster Buffet Food", "category": "furniture_item", "id": "2015", "label": "Monster Buffet Food", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Monster Library Table", "category": "furniture_item", "id": "2017", "label": "Monster Library Table", "cost": "250", "type": "1"}, {"is_member": "true", "prompt": "Monster Eye Pillar", "category": "furniture_item", "id": "2018", "label": "Monster Eye Pillar", "cost": "125", "type": "1"}, {"is_member": "true", "prompt": "Monster Door Station", "category": "furniture_item", "id": "2019", "label": "Monster Door Station", "cost": "450", "type": "1"}, {"is_member": "true", "prompt": "Monster Boombox", "category": "furniture_item", "id": "2023", "label": "Monster Boombox", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Monster Ping Pong Table", "category": "furniture_item", "id": "2024", "label": "Monster Ping Pong Table", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Monster Lounge Chair", "category": "furniture_item", "id": "2025", "label": "Monster Lounge Chair", "cost": "350", "type": "1"}, {"is_member": "true", "prompt": "Imperial Wall Panel", "category": "furniture_item", "id": "2026", "label": "Imperial Wall Panel", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Radar Screen", "category": "furniture_item", "id": "2027", "label": "Radar Screen", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Droid Cleaning Station", "category": "furniture_item", "id": "2028", "label": "Droid Cleaning Station", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Holonet Terminal", "category": "furniture_item", "id": "2029", "label": "Holonet Terminal", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Millennium Falcon Seats", "category": "furniture_item", "id": "2030", "label": "Millennium Falcon Seats", "cost": "250", "type": "1"}, {"is_member": "true", "prompt": "System Readout Terminal", "category": "furniture_item", "id": "2031", "label": "System Readout Terminal", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "<PERSON><PERSON><PERSON>", "category": "furniture_item", "id": "2032", "label": "<PERSON><PERSON><PERSON>", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "GNK Power Droid", "category": "furniture_item", "id": "2033", "label": "GNK Power Droid", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Imperial Archway", "category": "furniture_item", "id": "2034", "label": "Imperial Archway", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Computer Console", "category": "furniture_item", "id": "2035", "label": "Computer Console", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "LIN Demolitionmech Droid", "category": "furniture_item", "id": "2038", "label": "LIN Demolitionmech Droid", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "TIE Fighter Chair", "category": "furniture_item", "id": "2039", "label": "TIE Fighter Chair", "cost": "450", "type": "1"}, {"is_member": "true", "prompt": "X-wing Fighter Chair", "category": "furniture_item", "id": "2040", "label": "X-wing Fighter Chair", "cost": "450", "type": "1"}, {"is_member": "true", "prompt": "Tatooine House", "category": "furniture_item", "id": "2041", "label": "Tatooine House", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Imperial Throne", "category": "furniture_item", "id": "2042", "label": "Imperial Throne", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Holonet Tracking Console", "category": "furniture_item", "id": "2043", "label": "Holonet Tracking Console", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Radar Computer", "category": "furniture_item", "id": "2044", "label": "Radar Computer", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Swimming Pool", "category": "furniture_item", "id": "2046", "label": "Swimming Pool", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Board Wave", "category": "furniture_item", "id": "2047", "label": "Board Wave", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Mermaid Cutout", "category": "furniture_item", "id": "2048", "label": "Mermaid Cutout", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "<PERSON><PERSON> Cutout", "category": "furniture_item", "id": "2049", "label": "<PERSON><PERSON> Cutout", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Lit Stage", "category": "furniture_item", "id": "2050", "label": "Lit Stage", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Beach Beatz", "category": "furniture_item", "id": "2051", "label": "Beach Beatz", "cost": "250", "type": "1"}, {"is_member": "true", "prompt": "Dressing Table", "category": "furniture_item", "id": "2052", "label": "Dressing Table", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Retro Jukebox", "category": "furniture_item", "id": "2053", "label": "Retro Jukebox", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Inflatable Lounge Chair", "category": "furniture_item", "id": "2054", "label": "Inflatable Lounge Chair", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Dressing Screen", "category": "furniture_item", "id": "2055", "label": "Dressing Screen", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Lifeguard Chair", "category": "furniture_item", "id": "2056", "label": "Lifeguard Chair", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Diving Board", "category": "furniture_item", "id": "2058", "label": "Diving Board", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Inner Tube", "category": "furniture_item", "id": "2059", "label": "Inner Tube", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "<PERSON> <PERSON><PERSON>'s Diner", "category": "furniture_item", "id": "2060", "label": "<PERSON> <PERSON><PERSON>'s Diner", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Mic", "category": "furniture_item", "id": "2061", "label": "Mic", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Beach Boards", "category": "furniture_item", "id": "2062", "label": "Beach Boards", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Motorbike", "category": "furniture_item", "id": "2063", "label": "Motorbike", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Gas Pump", "category": "furniture_item", "id": "2064", "label": "Gas Pump", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Castle Gate", "category": "furniture_item", "id": "2065", "label": "Castle Gate", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Stone Keep", "category": "furniture_item", "id": "2066", "label": "Stone Keep", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Watch Tower", "category": "furniture_item", "id": "2067", "label": "Watch Tower", "cost": "75", "type": "1"}, {"is_member": "true", "prompt": "Castle Entrance", "category": "furniture_item", "id": "2068", "label": "Castle Entrance", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Battlements", "category": "furniture_item", "id": "2069", "label": "Battlements", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "<PERSON><PERSON><PERSON>", "category": "furniture_item", "id": "2071", "label": "<PERSON><PERSON><PERSON>", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Ogre Table", "category": "furniture_item", "id": "2073", "label": "Ogre Table", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Fairy Tree Stump", "category": "furniture_item", "id": "2074", "label": "Fairy Tree Stump", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Fairy Flower", "category": "furniture_item", "id": "2075", "label": "Fairy Flower", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Steel Anvil", "category": "furniture_item", "id": "2076", "label": "Steel Anvil", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Wish You Well", "category": "furniture_item", "id": "2077", "label": "Wish You Well", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Regal Throne", "category": "furniture_item", "id": "2078", "label": "Regal Throne", "cost": "300", "type": "1"}, {"is_member": "true", "prompt": "Potions Table", "category": "furniture_item", "id": "2079", "label": "Potions Table", "cost": "200", "type": "1"}, {"is_member": "true", "prompt": "Royal Cannon", "category": "furniture_item", "id": "2081", "label": "Royal Cannon", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Magical Fairy Plant", "category": "furniture_item", "id": "2082", "label": "Magical Fairy Plant", "cost": "150", "type": "1"}, {"is_member": "true", "prompt": "Barrel Top", "category": "furniture_item", "id": "2083", "label": "Barrel Top", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Ye Olde Puffle Bowl", "category": "furniture_item", "id": "2084", "label": "Ye Olde Puffle Bowl", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "<PERSON><PERSON> <PERSON>", "category": "furniture_item", "id": "2085", "label": "<PERSON><PERSON> <PERSON>", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "Stone Gatepost", "category": "furniture_item", "id": "2087", "label": "Stone Gatepost", "cost": "50", "type": "1"}, {"is_member": "true", "prompt": "<PERSON><PERSON> Cabinet", "category": "furniture_item", "id": "2088", "label": "<PERSON><PERSON> Cabinet", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Gnarled Tree", "category": "furniture_item", "id": "2089", "label": "Gnarled Tree", "cost": "100", "type": "1"}, {"is_member": "true", "prompt": "Snowy Backyard Igloo", "category": "igloo", "id": "30", "label": "Snowy Backyard Igloo", "cost": "3000"}, {"is_member": "true", "prompt": "Deluxe Snow Igloo", "category": "igloo", "id": "9", "label": "Deluxe Snow Igloo", "cost": "3000"}, {"is_member": "true", "prompt": "Split Level Igloo", "category": "igloo", "id": "13", "label": "Split Level Igloo", "cost": "4600"}, {"is_member": "true", "prompt": "Penthouse", "category": "igloo", "id": "57", "label": "Penthouse", "cost": "4000"}, {"is_member": "true", "prompt": "Beach Party Igloo", "category": "igloo", "id": "63", "label": "Beach Party Igloo", "cost": "1500"}, {"is_member": "true", "prompt": "Magical Hideout", "category": "igloo", "id": "65", "label": "Magical Hideout", "cost": "1500"}, {"is_member": "true", "prompt": "Eerie Castle", "category": "igloo", "id": "66", "label": "Eerie Castle", "cost": "2000"}, {"is_member": "true", "prompt": "Imperial Base Igloo", "category": "igloo", "id": "62", "label": "Imperial Base Igloo", "cost": "1000"}, {"is_member": "true", "prompt": "Train Station Igloo", "category": "igloo", "id": "68", "label": "Train Station Igloo", "cost": "1100"}, {"is_member": "true", "prompt": "Main Event Igloo", "category": "igloo", "id": "69", "label": "Main Event Igloo", "cost": "1000"}, {"is_member": "true", "prompt": "CP Airliner", "category": "igloo", "id": "70", "label": "CP Airliner", "cost": "1200"}, {"is_member": "true", "prompt": "Puffle Tree House", "category": "igloo", "id": "71", "label": "Puffle Tree House", "cost": "1500"}, {"is_member": "true", "prompt": "Secret Base", "category": "igloo", "id": "61", "label": "Secret Base", "cost": "1600"}, {"is_member": "true", "prompt": "Space Dome Igloo", "category": "igloo", "id": "73", "label": "Space Dome Igloo", "cost": "2000"}, {"is_member": "true", "prompt": "Tour Bus Igloo", "category": "igloo", "id": "75", "label": "Tour Bus Igloo", "cost": "1800"}, {"is_member": "true", "prompt": "Talent Show Stage", "category": "igloo", "id": "84", "label": "Talent Show Stage", "cost": "1500"}, {"is_member": "true", "prompt": "Beach", "category": "igloo_location", "id": "2", "label": "Beach", "cost": "2800"}, {"is_member": "true", "prompt": "Forest", "category": "igloo_location", "id": "3", "label": "Forest", "cost": "2700"}, {"is_member": "true", "prompt": "Mountain", "category": "igloo_location", "id": "4", "label": "Mountain", "cost": "2800"}, {"is_member": "true", "prompt": "Distant Planet", "category": "igloo_location", "id": "6", "label": "Distant Planet", "cost": "1800"}, {"is_member": "true", "prompt": "Soccer Pitch", "category": "igloo_location", "id": "7", "label": "Soccer Pitch", "cost": "1800"}, {"is_member": "true", "prompt": "Undersea", "category": "igloo_location", "id": "8", "label": "Undersea", "cost": "1800"}], "components": [{"name": "iglooedit_fc", "layout": {"images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/752924a4-f375-4540-9396-8c6d0c4a9677_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page01", "layout": {"images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/aa90d5c7-ee6d-46f3-8b6c-de6e48903c1f_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page02", "layout": {"images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/aa90d5c7-ee6d-46f3-8b6c-de6e48903c1f_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page03", "layout": {"frames": [{"name": "Black TV Stand", "originX": 91, "originY": 148, "width": 118, "height": 140, "configurator_id": 2347, "configurator_category": "furniture_item"}, {"name": "Gray TV Stand", "originX": 287, "originY": 155, "width": 118, "height": 140, "configurator_id": 2348, "configurator_category": "furniture_item"}, {"name": "Multi Shelves", "originX": 91, "originY": 365, "width": 118, "height": 140, "configurator_id": 2352, "configurator_category": "furniture_item"}, {"name": "Wood Shelves", "originX": 289, "originY": 366, "width": 118, "height": 140, "configurator_id": 2353, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/3e743191-c758-44f4-8092-b2926caa181b_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page04", "layout": {"frames": [{"name": "Black TV Stand", "originX": -406, "originY": 148, "width": 118, "height": 140, "configurator_id": 2347, "configurator_category": "furniture_item"}, {"name": "Gray TV Stand", "originX": -210, "originY": 155, "width": 118, "height": 140, "configurator_id": 2348, "configurator_category": "furniture_item"}, {"name": "Black Designer Couch", "originX": 142, "originY": 312, "width": 215, "height": 215, "configurator_id": 2349, "configurator_category": "furniture_item"}, {"name": "Brown Designer Couch", "originX": 293, "originY": 152, "width": 118, "height": 140, "configurator_id": 2350, "configurator_category": "furniture_item"}, {"name": "Red Designer Couch", "originX": 98, "originY": 148, "width": 118, "height": 140, "configurator_id": 2351, "configurator_category": "furniture_item"}, {"name": "Multi Shelves", "originX": -406, "originY": 365, "width": 118, "height": 140, "configurator_id": 2352, "configurator_category": "furniture_item"}, {"name": "Wood Shelves", "originX": -208, "originY": 366, "width": 118, "height": 140, "configurator_id": 2353, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/3e743191-c758-44f4-8092-b2926caa181b_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page05", "layout": {"frames": [{"name": "CP Air Seat", "originX": 33, "originY": 122, "width": 118, "height": 140, "configurator_id": 2202, "configurator_category": "furniture_item"}, {"name": "CP Air Pilot's Chair", "originX": 190, "originY": 122, "width": 118, "height": 140, "configurator_id": 2203, "configurator_category": "furniture_item"}, {"name": "Check-in Terminal", "originX": 344, "originY": 325, "width": 118, "height": 140, "configurator_id": 2204, "configurator_category": "furniture_item"}, {"name": "Gate Chair", "originX": 345, "originY": 122, "width": 118, "height": 140, "configurator_id": 2205, "configurator_category": "furniture_item"}, {"name": "Airport Departures Board", "originX": 184, "originY": 301, "width": 141, "height": 165, "configurator_id": 2206, "configurator_category": "furniture_item"}, {"name": "Elastic Barrier", "originX": 34, "originY": 323, "width": 118, "height": 140, "configurator_id": 2207, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/51bfeb31-35c2-46ea-b066-30000f1b2c06_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page06", "layout": {"frames": [{"name": "CP Air Seat", "originX": -464, "originY": 122, "width": 118, "height": 140, "configurator_id": 2202, "configurator_category": "furniture_item"}, {"name": "CP Air Pilot's Chair", "originX": -307, "originY": 122, "width": 118, "height": 140, "configurator_id": 2203, "configurator_category": "furniture_item"}, {"name": "Check-in Terminal", "originX": -153, "originY": 325, "width": 118, "height": 140, "configurator_id": 2204, "configurator_category": "furniture_item"}, {"name": "Gate Chair", "originX": -152, "originY": 122, "width": 118, "height": 140, "configurator_id": 2205, "configurator_category": "furniture_item"}, {"name": "Airport Departures Board", "originX": -313, "originY": 301, "width": 141, "height": 165, "configurator_id": 2206, "configurator_category": "furniture_item"}, {"name": "Elastic Barrier", "originX": -463, "originY": 323, "width": 118, "height": 140, "configurator_id": 2207, "configurator_category": "furniture_item"}, {"name": "Construction Pylon", "originX": 34, "originY": 124, "width": 118, "height": 140, "configurator_id": 562, "configurator_category": "furniture_item"}, {"name": "Construction Sign", "originX": 196, "originY": 127, "width": 118, "height": 140, "configurator_id": 564, "configurator_category": "furniture_item"}, {"name": "Newspaper Stand", "originX": 115, "originY": 320, "width": 277, "height": 152, "configurator_id": 865, "configurator_category": "furniture_item"}, {"name": "Footlight Stage", "originX": 351, "originY": 128, "width": 118, "height": 140, "configurator_id": 2211, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/51bfeb31-35c2-46ea-b066-30000f1b2c06_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page07", "layout": {"frames": [{"name": "Food Stand", "originX": 95, "originY": 35, "width": 339, "height": 188, "configurator_id": 757, "configurator_category": "furniture_item"}, {"name": "Balloon Bunch", "originX": 112, "originY": 242, "width": 118, "height": 140, "configurator_id": 749, "configurator_category": "furniture_item"}, {"name": "Patio Parasol", "originX": 307, "originY": 242, "width": 118, "height": 140, "configurator_id": 2280, "configurator_category": "furniture_item"}, {"name": "Popcorn Cart", "originX": 109, "originY": 429, "width": 118, "height": 140, "configurator_id": 2189, "configurator_category": "furniture_item"}, {"name": "Beach Party Ball", "originX": 305, "originY": 429, "width": 118, "height": 140, "configurator_id": 2277, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/02cd0308-bb67-4ac7-87d4-275aae31a941_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page08", "layout": {"frames": [{"name": "Food Stand", "originX": -402, "originY": 35, "width": 339, "height": 188, "configurator_id": 757, "configurator_category": "furniture_item"}, {"name": "Balloon Bunch", "originX": -385, "originY": 242, "width": 118, "height": 140, "configurator_id": 749, "configurator_category": "furniture_item"}, {"name": "Patio Parasol", "originX": -190, "originY": 242, "width": 118, "height": 140, "configurator_id": 2280, "configurator_category": "furniture_item"}, {"name": "Popcorn Cart", "originX": -388, "originY": 429, "width": 118, "height": 140, "configurator_id": 2189, "configurator_category": "furniture_item"}, {"name": "Beach Party Ball", "originX": -192, "originY": 429, "width": 118, "height": 140, "configurator_id": 2277, "configurator_category": "furniture_item"}, {"name": "Stone Column R<PERSON>s", "originX": 102, "originY": 62, "width": 118, "height": 140, "configurator_id": 548, "configurator_category": "furniture_item"}, {"name": "Sea Stones", "originX": 293, "originY": 60, "width": 118, "height": 140, "configurator_id": 808, "configurator_category": "furniture_item"}, {"name": "Pirate Barrel", "originX": 102, "originY": 248, "width": 118, "height": 140, "configurator_id": 2303, "configurator_category": "furniture_item"}, {"name": "Ship's Wheel", "originX": 293, "originY": 247, "width": 118, "height": 140, "configurator_id": 2316, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON> Chair", "originX": 102, "originY": 435, "width": 118, "height": 140, "configurator_id": 2187, "configurator_category": "furniture_item"}, {"name": "Wooden Crate", "originX": 293, "originY": 433, "width": 118, "height": 140, "configurator_id": 165, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/02cd0308-bb67-4ac7-87d4-275aae31a941_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page09", "layout": {"images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/0fc58c17-4555-4674-9a08-e1aca21f1aeb_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page10", "layout": {"frames": [{"name": "In Half Igloo", "originX": 278, "originY": 403, "width": 118, "height": 140, "configurator_id": 37, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/0fc58c17-4555-4674-9a08-e1aca21f1aeb_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page11", "layout": {"frames": [{"name": "Tent", "originX": 293, "originY": 399, "width": 118, "height": 140, "configurator_id": 19, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/ec355491-1961-4326-b7a1-50f6a90dcd1f_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page12", "layout": {"frames": [{"name": "Tent", "originX": -204, "originY": 399, "width": 118, "height": 140, "configurator_id": 19, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/ec355491-1961-4326-b7a1-50f6a90dcd1f_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page13", "layout": {"frames": [{"name": "Log Cabin", "originX": 298, "originY": 404, "width": 118, "height": 140, "configurator_id": 11, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/503a501d-9d51-4c44-a262-022c94cffd9e_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page14", "layout": {"frames": [{"name": "Log Cabin", "originX": -198, "originY": 404, "width": 118, "height": 140, "configurator_id": 11, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/503a501d-9d51-4c44-a262-022c94cffd9e_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page15", "layout": {"frames": [{"name": "Ship Igloo", "originX": 293, "originY": 399, "width": 118, "height": 140, "configurator_id": 23, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/c84510bb-53eb-442b-9574-e4ba791e1b3d_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page16", "layout": {"frames": [{"name": "Ship Igloo", "originX": -204, "originY": 399, "width": 118, "height": 140, "configurator_id": 23, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/c84510bb-53eb-442b-9574-e4ba791e1b3d_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page17", "layout": {"frames": [{"name": "Dance Floor", "originX": 266, "originY": 448, "width": 118, "height": 140, "configurator_id": 7, "configurator_category": "igloo_floor"}, {"name": "Blue Turf", "originX": 42, "originY": 263, "width": 118, "height": 140, "configurator_id": 11, "configurator_category": "igloo_floor"}, {"name": "Phony Lawn", "originX": 342, "originY": 269, "width": 118, "height": 134, "configurator_id": 14, "configurator_category": "igloo_floor"}, {"name": "Black Carpet", "originX": 194, "originY": 72, "width": 118, "height": 140, "configurator_id": 15, "configurator_category": "igloo_floor"}, {"name": "Sunny Sky Floor", "originX": 122, "originY": 448, "width": 118, "height": 140, "configurator_id": 19, "configurator_category": "igloo_floor"}, {"name": "Snowy Floor", "originX": 194, "originY": 263, "width": 118, "height": 140, "configurator_id": 21, "configurator_category": "igloo_floor"}, {"name": "Lime Green Carpet", "originX": 342, "originY": 72, "width": 118, "height": 140, "configurator_id": 22, "configurator_category": "igloo_floor"}, {"name": "Pink Carpet", "originX": 42, "originY": 72, "width": 118, "height": 140, "configurator_id": 17, "configurator_category": "igloo_floor"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/462821f1-14fc-4d1a-b0f1-932c5adfe012_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page18", "layout": {"frames": [{"name": "Dance Floor", "originX": -231, "originY": 448, "width": 118, "height": 140, "configurator_id": 7, "configurator_category": "igloo_floor"}, {"name": "Blue Turf", "originX": -455, "originY": 263, "width": 118, "height": 140, "configurator_id": 11, "configurator_category": "igloo_floor"}, {"name": "Phony Lawn", "originX": -155, "originY": 269, "width": 118, "height": 134, "configurator_id": 14, "configurator_category": "igloo_floor"}, {"name": "Black Carpet", "originX": -303, "originY": 72, "width": 118, "height": 140, "configurator_id": 15, "configurator_category": "igloo_floor"}, {"name": "Sunny Sky Floor", "originX": -375, "originY": 448, "width": 118, "height": 140, "configurator_id": 19, "configurator_category": "igloo_floor"}, {"name": "Snowy Floor", "originX": -303, "originY": 263, "width": 118, "height": 140, "configurator_id": 21, "configurator_category": "igloo_floor"}, {"name": "Lime Green Carpet", "originX": -155, "originY": 72, "width": 118, "height": 140, "configurator_id": 22, "configurator_category": "igloo_floor"}, {"name": "Pink Carpet", "originX": -455, "originY": 72, "width": 118, "height": 140, "configurator_id": 17, "configurator_category": "igloo_floor"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/462821f1-14fc-4d1a-b0f1-932c5adfe012_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page19", "layout": {"frames": [{"name": "<PERSON><PERSON>", "originX": 103, "originY": 138, "width": 118, "height": 140, "configurator_id": 788, "configurator_category": "furniture_item"}, {"name": "Arm Chair", "originX": 260, "originY": 138, "width": 118, "height": 140, "configurator_id": 787, "configurator_category": "furniture_item"}, {"name": "Door Mat", "originX": 103, "originY": 304, "width": 118, "height": 140, "configurator_id": 792, "configurator_category": "furniture_item"}, {"name": "Fern", "originX": 260, "originY": 304, "width": 118, "height": 140, "configurator_id": 790, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/7f22efc3-d1fa-41ca-b2ed-f456a5a8e086_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page20", "layout": {"frames": [{"name": "<PERSON><PERSON>", "originX": -394, "originY": 138, "width": 118, "height": 140, "configurator_id": 788, "configurator_category": "furniture_item"}, {"name": "Arm Chair", "originX": -237, "originY": 138, "width": 118, "height": 140, "configurator_id": 787, "configurator_category": "furniture_item"}, {"name": "Mini Pumpkin Lanterns", "originX": 123, "originY": 304, "width": 118, "height": 140, "configurator_id": 2097, "configurator_category": "furniture_item"}, {"name": "Door Mat", "originX": -394, "originY": 304, "width": 118, "height": 140, "configurator_id": 792, "configurator_category": "furniture_item"}, {"name": "Fern", "originX": -237, "originY": 304, "width": 118, "height": 140, "configurator_id": 790, "configurator_category": "furniture_item"}, {"name": "Potted Poinsettia", "originX": 121, "originY": 138, "width": 118, "height": 140, "configurator_id": 2321, "configurator_category": "furniture_item"}, {"name": "Green Clover", "originX": 277, "originY": 138, "width": 118, "height": 140, "configurator_id": 609, "configurator_category": "furniture_item"}, {"name": "Inspiration Station", "originX": 279, "originY": 303, "width": 118, "height": 140, "configurator_id": 2201, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/7f22efc3-d1fa-41ca-b2ed-f456a5a8e086_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page21", "layout": {"frames": [{"name": "Mounted Speaker", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 2354, "configurator_category": "furniture_item"}, {"name": "Starship Panel", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 2331, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": 196, "originY": 385, "width": 118, "height": 140, "configurator_id": 2325, "configurator_category": "furniture_item"}, {"name": "Ice Cube Wreath", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 2318, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Curtains", "originX": 327, "originY": 385, "width": 118, "height": 140, "configurator_id": 2296, "configurator_category": "furniture_item"}, {"name": "Corner Wall Cabinet", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2262, "configurator_category": "furniture_item"}, {"name": "Double Wall Cabinet", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2261, "configurator_category": "furniture_item"}, {"name": "Wall Cabinet", "originX": 196, "originY": 73, "width": 118, "height": 140, "configurator_id": 2260, "configurator_category": "furniture_item"}, {"name": "Upper Cabinets", "originX": 327, "originY": 73, "width": 118, "height": 140, "configurator_id": 2257, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/669f9db8-9ae3-4410-9b37-7ec654e90375_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page22", "layout": {"frames": [{"name": "Mounted Speaker", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 2354, "configurator_category": "furniture_item"}, {"name": "Starship Panel", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 2331, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": -301, "originY": 385, "width": 118, "height": 140, "configurator_id": 2325, "configurator_category": "furniture_item"}, {"name": "Ice Cube Wreath", "originX": -439, "originY": 385, "width": 118, "height": 140, "configurator_id": 2318, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Curtains", "originX": -170, "originY": 385, "width": 118, "height": 140, "configurator_id": 2296, "configurator_category": "furniture_item"}, {"name": "Corner Wall Cabinet", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 2262, "configurator_category": "furniture_item"}, {"name": "Double Wall Cabinet", "originX": -439, "originY": 73, "width": 118, "height": 140, "configurator_id": 2261, "configurator_category": "furniture_item"}, {"name": "Wall Cabinet", "originX": -301, "originY": 73, "width": 118, "height": 140, "configurator_id": 2260, "configurator_category": "furniture_item"}, {"name": "Upper Cabinets", "originX": -170, "originY": 73, "width": 118, "height": 140, "configurator_id": 2257, "configurator_category": "furniture_item"}, {"name": "Go Sharks", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2243, "configurator_category": "furniture_item"}, {"name": "Go Space Squids", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 2244, "configurator_category": "furniture_item"}, {"name": "Go Hot Sauce", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 2245, "configurator_category": "furniture_item"}, {"name": "<PERSON> Flu<PERSON>ies", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2246, "configurator_category": "furniture_item"}, {"name": "Fish Fossil", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 2173, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON><PERSON>", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 2104, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 2103, "configurator_category": "furniture_item"}, {"name": "Scary Lantern Light", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 2102, "configurator_category": "furniture_item"}, {"name": "Window", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 560, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/669f9db8-9ae3-4410-9b37-7ec654e90375_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page23", "layout": {"frames": [{"name": "Neon Bat", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2100, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": 196, "originY": 73, "width": 118, "height": 140, "configurator_id": 2099, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2096, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 2095, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 2094, "configurator_category": "furniture_item"}, {"name": "Garland of Sweets", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 2093, "configurator_category": "furniture_item"}, {"name": "Ghastly Window", "originX": 196, "originY": 385, "width": 118, "height": 140, "configurator_id": 2092, "configurator_category": "furniture_item"}, {"name": "Ogre Puffle Head", "originX": 327, "originY": 385, "width": 118, "height": 140, "configurator_id": 2086, "configurator_category": "furniture_item"}, {"name": "Holiday Bells", "originX": 327, "originY": 73, "width": 118, "height": 140, "configurator_id": 590, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/fa101c57-ee22-4b50-a29e-45a9940ccf2f_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page24", "layout": {"frames": [{"name": "Neon Bat", "originX": -439, "originY": 73, "width": 118, "height": 140, "configurator_id": 2100, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": -301, "originY": 73, "width": 118, "height": 140, "configurator_id": 2099, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 2096, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 2095, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 2094, "configurator_category": "furniture_item"}, {"name": "Garland of Sweets", "originX": -439, "originY": 385, "width": 118, "height": 140, "configurator_id": 2093, "configurator_category": "furniture_item"}, {"name": "Ghastly Window", "originX": -301, "originY": 385, "width": 118, "height": 140, "configurator_id": 2092, "configurator_category": "furniture_item"}, {"name": "Ogre Puffle Head", "originX": -170, "originY": 385, "width": 118, "height": 140, "configurator_id": 2086, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON><PERSON>", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2072, "configurator_category": "furniture_item"}, {"name": "Regal Dr<PERSON>", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 2070, "configurator_category": "furniture_item"}, {"name": "Pink Curtains", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 2057, "configurator_category": "furniture_item"}, {"name": "Death Star", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2045, "configurator_category": "furniture_item"}, {"name": "3D ROR", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 2022, "configurator_category": "furniture_item"}, {"name": "ROR Pennant", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 2021, "configurator_category": "furniture_item"}, {"name": "Red Triangle Pennants", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 2020, "configurator_category": "furniture_item"}, {"name": "3D JOX", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 2011, "configurator_category": "furniture_item"}, {"name": "JOX Pennant", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 2010, "configurator_category": "furniture_item"}, {"name": "Holiday Bells", "originX": -170, "originY": 73, "width": 118, "height": 140, "configurator_id": 590, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/fa101c57-ee22-4b50-a29e-45a9940ccf2f_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page25", "layout": {"frames": [{"name": "3D PNK", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2009, "configurator_category": "furniture_item"}, {"name": "PNK <PERSON>", "originX": 196, "originY": 73, "width": 118, "height": 140, "configurator_id": 2008, "configurator_category": "furniture_item"}, {"name": "3D OK", "originX": 327, "originY": 73, "width": 118, "height": 140, "configurator_id": 2007, "configurator_category": "furniture_item"}, {"name": "OK Pennant", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2006, "configurator_category": "furniture_item"}, {"name": "Monster Scoreboard", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 2004, "configurator_category": "furniture_item"}, {"name": "Pink Triangle Pennants", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 2002, "configurator_category": "furniture_item"}, {"name": "Green Triangle Pennants", "originX": 196, "originY": 385, "width": 118, "height": 140, "configurator_id": 2001, "configurator_category": "furniture_item"}, {"name": "Floral Paper Screen", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 992, "configurator_category": "furniture_item"}, {"name": "Orange Triangle Pennants", "originX": 327, "originY": 385, "width": 118, "height": 140, "configurator_id": 2003, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/a308b638-dbf3-41ad-abdc-27cdbaadd4e0_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page26", "layout": {"frames": [{"name": "3D PNK", "originX": -439, "originY": 73, "width": 118, "height": 140, "configurator_id": 2009, "configurator_category": "furniture_item"}, {"name": "PNK <PERSON>", "originX": -301, "originY": 73, "width": 118, "height": 140, "configurator_id": 2008, "configurator_category": "furniture_item"}, {"name": "3D OK", "originX": -170, "originY": 73, "width": 118, "height": 140, "configurator_id": 2007, "configurator_category": "furniture_item"}, {"name": "OK Pennant", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 2006, "configurator_category": "furniture_item"}, {"name": "Monster Scoreboard", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 2004, "configurator_category": "furniture_item"}, {"name": "Pink Triangle Pennants", "originX": -439, "originY": 385, "width": 118, "height": 140, "configurator_id": 2002, "configurator_category": "furniture_item"}, {"name": "Green Triangle Pennants", "originX": -301, "originY": 385, "width": 118, "height": 140, "configurator_id": 2001, "configurator_category": "furniture_item"}, {"name": "Floral Paper Screen", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 992, "configurator_category": "furniture_item"}, {"name": "Orange Triangle Pennants", "originX": -170, "originY": 385, "width": 118, "height": 140, "configurator_id": 2003, "configurator_category": "furniture_item"}, {"name": "Earth", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 967, "configurator_category": "furniture_item"}, {"name": "Mars", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 966, "configurator_category": "furniture_item"}, {"name": "Leafy Window", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 946, "configurator_category": "furniture_item"}, {"name": "Full Moon", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 908, "configurator_category": "furniture_item"}, {"name": "Thatched Awning", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 884, "configurator_category": "furniture_item"}, {"name": "Show Lights", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 872, "configurator_category": "furniture_item"}, {"name": "Cityscape", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 861, "configurator_category": "furniture_item"}, {"name": "Dragon Flag", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 857, "configurator_category": "furniture_item"}, {"name": "Concert Lights", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 355, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/a308b638-dbf3-41ad-abdc-27cdbaadd4e0_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page27", "layout": {"frames": [{"name": "Sky Flag", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 856, "configurator_category": "furniture_item"}, {"name": "Scorn Flag", "originX": 196, "originY": 73, "width": 118, "height": 140, "configurator_id": 855, "configurator_category": "furniture_item"}, {"name": "Village Flag", "originX": 327, "originY": 73, "width": 118, "height": 140, "configurator_id": 854, "configurator_category": "furniture_item"}, {"name": "Wizard Flag", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 853, "configurator_category": "furniture_item"}, {"name": "Fairy Woods Flag", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 852, "configurator_category": "furniture_item"}, {"name": "Kingdom Flag", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 851, "configurator_category": "furniture_item"}, {"name": "Wispy Clouds", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 848, "configurator_category": "furniture_item"}, {"name": "Fun Fungus", "originX": 196, "originY": 385, "width": 118, "height": 140, "configurator_id": 847, "configurator_category": "furniture_item"}, {"name": "Cheeky Lantern", "originX": 327, "originY": 385, "width": 118, "height": 140, "configurator_id": 829, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/b27ca448-d69d-484b-ab38-266ae0ff27e1_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page28", "layout": {"frames": [{"name": "Sky Flag", "originX": -439, "originY": 73, "width": 118, "height": 140, "configurator_id": 856, "configurator_category": "furniture_item"}, {"name": "Scorn Flag", "originX": -301, "originY": 73, "width": 118, "height": 140, "configurator_id": 855, "configurator_category": "furniture_item"}, {"name": "Village Flag", "originX": -170, "originY": 73, "width": 118, "height": 140, "configurator_id": 854, "configurator_category": "furniture_item"}, {"name": "Wizard Flag", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 853, "configurator_category": "furniture_item"}, {"name": "Fairy Woods Flag", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 852, "configurator_category": "furniture_item"}, {"name": "Kingdom Flag", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 851, "configurator_category": "furniture_item"}, {"name": "Wispy Clouds", "originX": -439, "originY": 385, "width": 118, "height": 140, "configurator_id": 848, "configurator_category": "furniture_item"}, {"name": "Fun Fungus", "originX": -301, "originY": 385, "width": 118, "height": 140, "configurator_id": 847, "configurator_category": "furniture_item"}, {"name": "Cheeky Lantern", "originX": -170, "originY": 385, "width": 118, "height": 140, "configurator_id": 829, "configurator_category": "furniture_item"}, {"name": "Grumpy Lantern", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 828, "configurator_category": "furniture_item"}, {"name": "Laughing Lantern", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 827, "configurator_category": "furniture_item"}, {"name": "Happy Lantern", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 826, "configurator_category": "furniture_item"}, {"name": "Cozy Fireplace", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 804, "configurator_category": "furniture_item"}, {"name": "Holiday Tree Decoration", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 802, "configurator_category": "furniture_item"}, {"name": "Holiday Star Decoration", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 801, "configurator_category": "furniture_item"}, {"name": "Icing Decorations", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 799, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON><PERSON>", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 796, "configurator_category": "furniture_item"}, {"name": "Ornate Mirror", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 536, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/b27ca448-d69d-484b-ab38-266ae0ff27e1_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page29", "layout": {"frames": [{"name": "Purple Paper Lantern", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 785, "configurator_category": "furniture_item"}, {"name": "Yellow Paper Lantern", "originX": 196, "originY": 73, "width": 118, "height": 140, "configurator_id": 784, "configurator_category": "furniture_item"}, {"name": "Red Paper Lantern", "originX": 327, "originY": 73, "width": 118, "height": 140, "configurator_id": 783, "configurator_category": "furniture_item"}, {"name": "Blue Paper Lantern", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 782, "configurator_category": "furniture_item"}, {"name": "Green Paper Lantern", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 775, "configurator_category": "furniture_item"}, {"name": "Wall Bats", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 771, "configurator_category": "furniture_item"}, {"name": "Wall Ghosts", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 770, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": 196, "originY": 385, "width": 118, "height": 140, "configurator_id": 769, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON>", "originX": 327, "originY": 385, "width": 118, "height": 140, "configurator_id": 768, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/452f44e2-5bfe-447e-86a1-e878d7d86d71_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page30", "layout": {"frames": [{"name": "Purple Paper Lantern", "originX": -439, "originY": 73, "width": 118, "height": 140, "configurator_id": 785, "configurator_category": "furniture_item"}, {"name": "Yellow Paper Lantern", "originX": -301, "originY": 73, "width": 118, "height": 140, "configurator_id": 784, "configurator_category": "furniture_item"}, {"name": "Red Paper Lantern", "originX": -170, "originY": 73, "width": 118, "height": 140, "configurator_id": 783, "configurator_category": "furniture_item"}, {"name": "Blue Paper Lantern", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 782, "configurator_category": "furniture_item"}, {"name": "Green Paper Lantern", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 775, "configurator_category": "furniture_item"}, {"name": "Wall Bats", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 771, "configurator_category": "furniture_item"}, {"name": "Wall Ghosts", "originX": -439, "originY": 385, "width": 118, "height": 140, "configurator_id": 770, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": -301, "originY": 385, "width": 118, "height": 140, "configurator_id": 769, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON>", "originX": -170, "originY": 385, "width": 118, "height": 140, "configurator_id": 768, "configurator_category": "furniture_item"}, {"name": "Terrifying Tissue Ghost", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 767, "configurator_category": "furniture_item"}, {"name": "Jack-O-<PERSON>", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 766, "configurator_category": "furniture_item"}, {"name": "Snappy <PERSON>", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 729, "configurator_category": "furniture_item"}, {"name": "Swinging Vines", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 722, "configurator_category": "furniture_item"}, {"name": "Sea Streamers", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 721, "configurator_category": "furniture_item"}, {"name": "Hanging Algae", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 720, "configurator_category": "furniture_item"}, {"name": "Laser Lights", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 719, "configurator_category": "furniture_item"}, {"name": "Electric Encore", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 718, "configurator_category": "furniture_item"}, {"name": "Musical Motif", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 717, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/452f44e2-5bfe-447e-86a1-e878d7d86d71_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page31", "layout": {"frames": [{"name": "<PERSON><PERSON><PERSON>", "originX": 57, "originY": 381, "width": 118, "height": 140, "configurator_id": 716, "configurator_category": "furniture_item"}, {"name": "Hanging Moss", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 700, "configurator_category": "furniture_item"}, {"name": "Vines", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 699, "configurator_category": "furniture_item"}, {"name": "Ye Olde Blue Banner", "originX": 62, "originY": 72, "width": 118, "height": 140, "configurator_id": 697, "configurator_category": "furniture_item"}, {"name": "Ye Olde Red Banner", "originX": 200, "originY": 72, "width": 118, "height": 140, "configurator_id": 696, "configurator_category": "furniture_item"}, {"name": "Ye Olde Yellow Banner", "originX": 331, "originY": 72, "width": 118, "height": 140, "configurator_id": 698, "configurator_category": "furniture_item"}, {"name": "Island Trinkets", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 703, "configurator_category": "furniture_item"}, {"name": "Dream Catcher", "originX": 195, "originY": 382, "width": 118, "height": 140, "configurator_id": 662, "configurator_category": "furniture_item"}, {"name": "<PERSON> Puffle Picture", "originX": 331, "originY": 383, "width": 118, "height": 140, "configurator_id": 666, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/18d60274-1f8c-43cc-9ae7-7cbc933f5ba6_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page32", "layout": {"frames": [{"name": "<PERSON><PERSON><PERSON>", "originX": -440, "originY": 381, "width": 118, "height": 140, "configurator_id": 716, "configurator_category": "furniture_item"}, {"name": "Hanging Moss", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 700, "configurator_category": "furniture_item"}, {"name": "Vines", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 699, "configurator_category": "furniture_item"}, {"name": "Ye Olde Blue Banner", "originX": -435, "originY": 72, "width": 118, "height": 140, "configurator_id": 697, "configurator_category": "furniture_item"}, {"name": "Ye Olde Red Banner", "originX": -297, "originY": 72, "width": 118, "height": 140, "configurator_id": 696, "configurator_category": "furniture_item"}, {"name": "Ye Olde Yellow Banner", "originX": -166, "originY": 72, "width": 118, "height": 140, "configurator_id": 698, "configurator_category": "furniture_item"}, {"name": "Island Trinkets", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 703, "configurator_category": "furniture_item"}, {"name": "Dream Catcher", "originX": -302, "originY": 382, "width": 118, "height": 140, "configurator_id": 662, "configurator_category": "furniture_item"}, {"name": "Orange Puffle Picture", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 690, "configurator_category": "furniture_item"}, {"name": "Black Puffle Picture", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 674, "configurator_category": "furniture_item"}, {"name": "White Puffle Picture", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 673, "configurator_category": "furniture_item"}, {"name": "Yellow Puffle Picture", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 672, "configurator_category": "furniture_item"}, {"name": "Purple Puffle Picture", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 671, "configurator_category": "furniture_item"}, {"name": "Pink Puffle Picture", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 670, "configurator_category": "furniture_item"}, {"name": "Green Puffle Picture", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 669, "configurator_category": "furniture_item"}, {"name": "Red Puffle Picture", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 668, "configurator_category": "furniture_item"}, {"name": "Blue Puffle Picture", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 667, "configurator_category": "furniture_item"}, {"name": "<PERSON> Puffle Picture", "originX": -166, "originY": 383, "width": 118, "height": 140, "configurator_id": 666, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/18d60274-1f8c-43cc-9ae7-7cbc933f5ba6_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page33", "layout": {"frames": [{"name": "Confetti Blaster", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 710, "configurator_category": "furniture_item"}, {"name": "Hanging Basket", "originX": 327, "originY": 73, "width": 118, "height": 140, "configurator_id": 688, "configurator_category": "furniture_item"}, {"name": "Window Basket", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 685, "configurator_category": "furniture_item"}, {"name": "Stockings", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 659, "configurator_category": "furniture_item"}, {"name": "Snowflake", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 657, "configurator_category": "furniture_item"}, {"name": "Iron Chandelier", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 653, "configurator_category": "furniture_item"}, {"name": "Multi-Pane Window", "originX": 196, "originY": 385, "width": 118, "height": 140, "configurator_id": 652, "configurator_category": "furniture_item"}, {"name": "Perched <PERSON><PERSON>le St<PERSON>ue", "originX": 327, "originY": 385, "width": 118, "height": 140, "configurator_id": 643, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON>", "originX": 196, "originY": 73, "width": 118, "height": 140, "configurator_id": 2098, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/5bed983b-521c-4d64-83e7-fc5e590bee1f_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page34", "layout": {"frames": [{"name": "Confetti Blaster", "originX": -439, "originY": 73, "width": 118, "height": 140, "configurator_id": 710, "configurator_category": "furniture_item"}, {"name": "Hanging Basket", "originX": -170, "originY": 73, "width": 118, "height": 140, "configurator_id": 688, "configurator_category": "furniture_item"}, {"name": "Window Basket", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 685, "configurator_category": "furniture_item"}, {"name": "Stockings", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 659, "configurator_category": "furniture_item"}, {"name": "Snowflake", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 657, "configurator_category": "furniture_item"}, {"name": "Iron Chandelier", "originX": -439, "originY": 385, "width": 118, "height": 140, "configurator_id": 653, "configurator_category": "furniture_item"}, {"name": "Multi-Pane Window", "originX": -301, "originY": 385, "width": 118, "height": 140, "configurator_id": 652, "configurator_category": "furniture_item"}, {"name": "Perched <PERSON><PERSON>le St<PERSON>ue", "originX": -170, "originY": 385, "width": 118, "height": 140, "configurator_id": 643, "configurator_category": "furniture_item"}, {"name": "Yellow CP Banner", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 642, "configurator_category": "furniture_item"}, {"name": "White Board", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 641, "configurator_category": "furniture_item"}, {"name": "Scroll-down Map", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 640, "configurator_category": "furniture_item"}, {"name": "Classroom Bell", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 639, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 632, "configurator_category": "furniture_item"}, {"name": "Burgundy Curtains", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 624, "configurator_category": "furniture_item"}, {"name": "Rainbow with Pot O' Gold", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 616, "configurator_category": "furniture_item"}, {"name": "Clover Garland", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 611, "configurator_category": "furniture_item"}, {"name": "HD TV", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 596, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON>", "originX": -301, "originY": 73, "width": 118, "height": 140, "configurator_id": 2098, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/5bed983b-521c-4d64-83e7-fc5e590bee1f_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page35", "layout": {"frames": [{"name": "River's Edge", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2138, "configurator_category": "furniture_item"}, {"name": "Purple Pit", "originX": 196, "originY": 73, "width": 118, "height": 140, "configurator_id": 2108, "configurator_category": "furniture_item"}, {"name": "Magical Garden", "originX": 327, "originY": 73, "width": 118, "height": 140, "configurator_id": 2080, "configurator_category": "furniture_item"}, {"name": "Imperial House Rug", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2037, "configurator_category": "furniture_item"}, {"name": "Rebel House Rug", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 2036, "configurator_category": "furniture_item"}, {"name": "Blue Floor Cushion", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 997, "configurator_category": "furniture_item"}, {"name": "Rock Garden", "originX": 196, "originY": 385, "width": 118, "height": 140, "configurator_id": 996, "configurator_category": "furniture_item"}, {"name": "Stone Walk Way", "originX": 327, "originY": 385, "width": 118, "height": 140, "configurator_id": 993, "configurator_category": "furniture_item"}, {"name": "Monster Rug", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 2016, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/00dec597-c41a-4af8-baa7-20b72ecc2f56_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page36", "layout": {"frames": [{"name": "River's Edge", "originX": -439, "originY": 73, "width": 118, "height": 140, "configurator_id": 2138, "configurator_category": "furniture_item"}, {"name": "Purple Pit", "originX": -301, "originY": 73, "width": 118, "height": 140, "configurator_id": 2108, "configurator_category": "furniture_item"}, {"name": "Magical Garden", "originX": -170, "originY": 73, "width": 118, "height": 140, "configurator_id": 2080, "configurator_category": "furniture_item"}, {"name": "Imperial House Rug", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 2037, "configurator_category": "furniture_item"}, {"name": "Rebel House Rug", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 2036, "configurator_category": "furniture_item"}, {"name": "Blue Floor Cushion", "originX": -439, "originY": 385, "width": 118, "height": 140, "configurator_id": 997, "configurator_category": "furniture_item"}, {"name": "Rock Garden", "originX": -301, "originY": 385, "width": 118, "height": 140, "configurator_id": 996, "configurator_category": "furniture_item"}, {"name": "Stone Walk Way", "originX": -170, "originY": 385, "width": 118, "height": 140, "configurator_id": 993, "configurator_category": "furniture_item"}, {"name": "Puffle Stage", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 980, "configurator_category": "furniture_item"}, {"name": "Rainbow Bridge", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 976, "configurator_category": "furniture_item"}, {"name": "Skating Rink", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 975, "configurator_category": "furniture_item"}, {"name": "Basketball Court", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 960, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 924, "configurator_category": "furniture_item"}, {"name": "Ectoplasmic Crevasse", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 911, "configurator_category": "furniture_item"}, {"name": "Manhole", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 863, "configurator_category": "furniture_item"}, {"name": "Watering Hole", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 837, "configurator_category": "furniture_item"}, {"name": "Surf <PERSON> Towel", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 183, "configurator_category": "furniture_item"}, {"name": "Monster Rug", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 2016, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/00dec597-c41a-4af8-baa7-20b72ecc2f56_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page37", "layout": {"frames": [{"name": "<PERSON><PERSON><PERSON> Rug", "originX": 62, "originY": 72, "width": 118, "height": 140, "configurator_id": 830, "configurator_category": "furniture_item"}, {"name": "Card-Jitsu Mat", "originX": 200, "originY": 72, "width": 118, "height": 140, "configurator_id": 786, "configurator_category": "furniture_item"}, {"name": "Swamp Slime", "originX": 331, "originY": 72, "width": 118, "height": 140, "configurator_id": 773, "configurator_category": "furniture_item"}, {"name": "Weathered Path", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 745, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON>", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 743, "configurator_category": "furniture_item"}, {"name": "Half-pipe", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 739, "configurator_category": "furniture_item"}, {"name": "Dance Mat", "originX": 57, "originY": 381, "width": 118, "height": 140, "configurator_id": 713, "configurator_category": "furniture_item"}, {"name": "Trap Door", "originX": 195, "originY": 382, "width": 118, "height": 140, "configurator_id": 692, "configurator_category": "furniture_item"}, {"name": "<PERSON>n <PERSON>", "originX": 331, "originY": 383, "width": 118, "height": 140, "configurator_id": 647, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/316b45f9-5745-45d0-ae11-dfb620ab326b_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page38", "layout": {"frames": [{"name": "<PERSON><PERSON><PERSON> Rug", "originX": -435, "originY": 72, "width": 118, "height": 140, "configurator_id": 830, "configurator_category": "furniture_item"}, {"name": "Card-Jitsu Mat", "originX": -297, "originY": 72, "width": 118, "height": 140, "configurator_id": 786, "configurator_category": "furniture_item"}, {"name": "Swamp Slime", "originX": -166, "originY": 72, "width": 118, "height": 140, "configurator_id": 773, "configurator_category": "furniture_item"}, {"name": "Weathered Path", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 745, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON>", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 743, "configurator_category": "furniture_item"}, {"name": "Half-pipe", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 739, "configurator_category": "furniture_item"}, {"name": "Dance Mat", "originX": -440, "originY": 381, "width": 118, "height": 140, "configurator_id": 713, "configurator_category": "furniture_item"}, {"name": "Trap Door", "originX": -302, "originY": 382, "width": 118, "height": 140, "configurator_id": 692, "configurator_category": "furniture_item"}, {"name": "<PERSON>n <PERSON>", "originX": -166, "originY": 383, "width": 118, "height": 140, "configurator_id": 647, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON>", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 646, "configurator_category": "furniture_item"}, {"name": "Ice Fishing Decal", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 607, "configurator_category": "furniture_item"}, {"name": "Blue Rug", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 606, "configurator_category": "furniture_item"}, {"name": "Puzzle Floor", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 571, "configurator_category": "furniture_item"}, {"name": "Bowling Alley", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 565, "configurator_category": "furniture_item"}, {"name": "Celtic Rug", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 535, "configurator_category": "furniture_item"}, {"name": "Tennis Court", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 522, "configurator_category": "furniture_item"}, {"name": "Pitcher's Mound", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 521, "configurator_category": "furniture_item"}, {"name": "Map Area Rug", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 494, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/316b45f9-5745-45d0-ae11-dfb620ab326b_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page39", "layout": {"frames": [{"name": "Modern Coffee Table", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2248, "configurator_category": "furniture_item"}, {"name": "Modern End Table", "originX": 196, "originY": 73, "width": 118, "height": 140, "configurator_id": 2249, "configurator_category": "furniture_item"}, {"name": "Granite Top Dishwasher", "originX": 327, "originY": 73, "width": 118, "height": 140, "configurator_id": 2250, "configurator_category": "furniture_item"}, {"name": "Granite Top Corner Cabinet", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2251, "configurator_category": "furniture_item"}, {"name": "Granite Top Cabinet", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 2252, "configurator_category": "furniture_item"}, {"name": "Granite Sink", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 2253, "configurator_category": "furniture_item"}, {"name": "Granite Top Double Cabinet", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 2254, "configurator_category": "furniture_item"}, {"name": "Brushed Steel Fridge", "originX": 196, "originY": 385, "width": 118, "height": 140, "configurator_id": 2255, "configurator_category": "furniture_item"}, {"name": "Granite Kitchen Island", "originX": 327, "originY": 385, "width": 118, "height": 140, "configurator_id": 2256, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/7b2ed404-b316-46d8-826e-4c28ade6d3df_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page40", "layout": {"frames": [{"name": "Modern Coffee Table", "originX": -439, "originY": 73, "width": 118, "height": 140, "configurator_id": 2248, "configurator_category": "furniture_item"}, {"name": "Modern End Table", "originX": -301, "originY": 73, "width": 118, "height": 140, "configurator_id": 2249, "configurator_category": "furniture_item"}, {"name": "Granite Top Dishwasher", "originX": -170, "originY": 73, "width": 118, "height": 140, "configurator_id": 2250, "configurator_category": "furniture_item"}, {"name": "Granite Top Corner Cabinet", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 2251, "configurator_category": "furniture_item"}, {"name": "Granite Top Cabinet", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 2252, "configurator_category": "furniture_item"}, {"name": "Granite Sink", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 2253, "configurator_category": "furniture_item"}, {"name": "Granite Top Double Cabinet", "originX": -439, "originY": 385, "width": 118, "height": 140, "configurator_id": 2254, "configurator_category": "furniture_item"}, {"name": "Brushed Steel Fridge", "originX": -301, "originY": 385, "width": 118, "height": 140, "configurator_id": 2255, "configurator_category": "furniture_item"}, {"name": "Granite Kitchen Island", "originX": -170, "originY": 385, "width": 118, "height": 140, "configurator_id": 2256, "configurator_category": "furniture_item"}, {"name": "Brushed Steel Oven", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2258, "configurator_category": "furniture_item"}, {"name": "Kitchen Stool", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 2259, "configurator_category": "furniture_item"}, {"name": "Cardboard Herbert", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 917, "configurator_category": "furniture_item"}, {"name": "Safe", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 918, "configurator_category": "furniture_item"}, {"name": "Containment Cell", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 919, "configurator_category": "furniture_item"}, {"name": "Emergency Light", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 920, "configurator_category": "furniture_item"}, {"name": "Paw Print", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 921, "configurator_category": "furniture_item"}, {"name": "Spy Car", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 922, "configurator_category": "furniture_item"}, {"name": "<PERSON>", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 925, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/7b2ed404-b316-46d8-826e-4c28ade6d3df_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page41", "layout": {"frames": [{"name": "<PERSON>hrone", "originX": 62, "originY": 72, "width": 118, "height": 140, "configurator_id": 926, "configurator_category": "furniture_item"}, {"name": "Cozy Red House", "originX": 200, "originY": 72, "width": 118, "height": 140, "configurator_id": 930, "configurator_category": "furniture_item"}, {"name": "Cozy Blue Door", "originX": 331, "originY": 72, "width": 118, "height": 140, "configurator_id": 931, "configurator_category": "furniture_item"}, {"name": "Tinker Train Engine", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 932, "configurator_category": "furniture_item"}, {"name": "Tinker Train Car", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 933, "configurator_category": "furniture_item"}, {"name": "Holiday Fireplace", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 935, "configurator_category": "furniture_item"}, {"name": "Hollow Tree", "originX": 195, "originY": 382, "width": 118, "height": 140, "configurator_id": 937, "configurator_category": "furniture_item"}, {"name": "Trusty Post", "originX": 331, "originY": 383, "width": 118, "height": 140, "configurator_id": 938, "configurator_category": "furniture_item"}, {"name": "Cozy Green House", "originX": 57, "originY": 381, "width": 118, "height": 140, "configurator_id": 929, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/f00d87f9-a084-42ce-b66a-714ccd7cf28a_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page42", "layout": {"frames": [{"name": "<PERSON>hrone", "originX": -435, "originY": 72, "width": 118, "height": 140, "configurator_id": 926, "configurator_category": "furniture_item"}, {"name": "Cozy Red House", "originX": -297, "originY": 72, "width": 118, "height": 140, "configurator_id": 930, "configurator_category": "furniture_item"}, {"name": "Cozy Blue Door", "originX": -166, "originY": 72, "width": 118, "height": 140, "configurator_id": 931, "configurator_category": "furniture_item"}, {"name": "Tinker Train Engine", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 932, "configurator_category": "furniture_item"}, {"name": "Tinker Train Car", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 933, "configurator_category": "furniture_item"}, {"name": "Holiday Fireplace", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 935, "configurator_category": "furniture_item"}, {"name": "Hollow Tree", "originX": -302, "originY": 382, "width": 118, "height": 140, "configurator_id": 937, "configurator_category": "furniture_item"}, {"name": "Trusty Post", "originX": -166, "originY": 383, "width": 118, "height": 140, "configurator_id": 938, "configurator_category": "furniture_item"}, {"name": "Wooden Walk", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 939, "configurator_category": "furniture_item"}, {"name": "Trustier Post", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 940, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON>", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 941, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON>", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 942, "configurator_category": "furniture_item"}, {"name": "Lava Pool", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 943, "configurator_category": "furniture_item"}, {"name": "Wooden Steps", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 944, "configurator_category": "furniture_item"}, {"name": "Short Wooden Steps", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 945, "configurator_category": "furniture_item"}, {"name": "Tall Grass", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 947, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON>", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 936, "configurator_category": "furniture_item"}, {"name": "Cozy Green House", "originX": -440, "originY": 381, "width": 118, "height": 140, "configurator_id": 929, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/f00d87f9-a084-42ce-b66a-714ccd7cf28a_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page43", "layout": {"frames": [{"name": "<PERSON><PERSON>", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 948, "configurator_category": "furniture_item"}, {"name": "Comfy Stump", "originX": 196, "originY": 73, "width": 118, "height": 140, "configurator_id": 949, "configurator_category": "furniture_item"}, {"name": "Space Age Lights", "originX": 327, "originY": 73, "width": 118, "height": 140, "configurator_id": 959, "configurator_category": "furniture_item"}, {"name": "Grand Piano", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 961, "configurator_category": "furniture_item"}, {"name": "DJ <PERSON>", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 962, "configurator_category": "furniture_item"}, {"name": "Jumbo Remote", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 963, "configurator_category": "furniture_item"}, {"name": "Diner Counter", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 964, "configurator_category": "furniture_item"}, {"name": "Jumbo TV", "originX": 196, "originY": 385, "width": 118, "height": 140, "configurator_id": 965, "configurator_category": "furniture_item"}, {"name": "Basketball Hoop", "originX": 327, "originY": 385, "width": 118, "height": 140, "configurator_id": 968, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/51d02a98-7331-4e6e-8201-868337e7723f_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page44", "layout": {"frames": [{"name": "<PERSON><PERSON>", "originX": -439, "originY": 73, "width": 118, "height": 140, "configurator_id": 948, "configurator_category": "furniture_item"}, {"name": "Comfy Stump", "originX": -301, "originY": 73, "width": 118, "height": 140, "configurator_id": 949, "configurator_category": "furniture_item"}, {"name": "Space Age Lights", "originX": -170, "originY": 73, "width": 118, "height": 140, "configurator_id": 959, "configurator_category": "furniture_item"}, {"name": "Grand Piano", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 961, "configurator_category": "furniture_item"}, {"name": "DJ <PERSON>", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 962, "configurator_category": "furniture_item"}, {"name": "Jumbo Remote", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 963, "configurator_category": "furniture_item"}, {"name": "Diner Counter", "originX": -439, "originY": 385, "width": 118, "height": 140, "configurator_id": 964, "configurator_category": "furniture_item"}, {"name": "Jumbo TV", "originX": -301, "originY": 385, "width": 118, "height": 140, "configurator_id": 965, "configurator_category": "furniture_item"}, {"name": "Basketball Hoop", "originX": -170, "originY": 385, "width": 118, "height": 140, "configurator_id": 968, "configurator_category": "furniture_item"}, {"name": "Spaceship", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 970, "configurator_category": "furniture_item"}, {"name": "Puffle Carrier", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 973, "configurator_category": "furniture_item"}, {"name": "Arcade Game", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 974, "configurator_category": "furniture_item"}, {"name": "Puffle Shop Shelf", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 977, "configurator_category": "furniture_item"}, {"name": "Puffle Shop Till", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 978, "configurator_category": "furniture_item"}, {"name": "Unicycle Tightrope", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 979, "configurator_category": "furniture_item"}, {"name": "Clinic Entrance", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 984, "configurator_category": "furniture_item"}, {"name": "General Store Front", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 985, "configurator_category": "furniture_item"}, {"name": "X-Ray Machine", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 986, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/51d02a98-7331-4e6e-8201-868337e7723f_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page45", "layout": {"frames": [{"name": "Hospital Chair", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 987, "configurator_category": "furniture_item"}, {"name": "Operating Room Lights", "originX": 196, "originY": 73, "width": 118, "height": 140, "configurator_id": 988, "configurator_category": "furniture_item"}, {"name": "Deluxe Tool Chest", "originX": 327, "originY": 73, "width": 118, "height": 140, "configurator_id": 989, "configurator_category": "furniture_item"}, {"name": "Sword Display", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 999, "configurator_category": "furniture_item"}, {"name": "Monster Bleachers", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 2000, "configurator_category": "furniture_item"}, {"name": "Monster Archway", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 2005, "configurator_category": "furniture_item"}, {"name": "Monster Table", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 2012, "configurator_category": "furniture_item"}, {"name": "Monster Library Shelves", "originX": 196, "originY": 385, "width": 118, "height": 140, "configurator_id": 2013, "configurator_category": "furniture_item"}, {"name": "Scare Can", "originX": 327, "originY": 385, "width": 118, "height": 140, "configurator_id": 2014, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/a84599cf-cf92-4f05-9c09-3931fc3c9fdf_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page46", "layout": {"frames": [{"name": "Hospital Chair", "originX": -439, "originY": 73, "width": 118, "height": 140, "configurator_id": 987, "configurator_category": "furniture_item"}, {"name": "Operating Room Lights", "originX": -301, "originY": 73, "width": 118, "height": 140, "configurator_id": 988, "configurator_category": "furniture_item"}, {"name": "Deluxe Tool Chest", "originX": -170, "originY": 73, "width": 118, "height": 140, "configurator_id": 989, "configurator_category": "furniture_item"}, {"name": "Sword Display", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 999, "configurator_category": "furniture_item"}, {"name": "Monster Bleachers", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 2000, "configurator_category": "furniture_item"}, {"name": "Monster Archway", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 2005, "configurator_category": "furniture_item"}, {"name": "Monster Table", "originX": -439, "originY": 385, "width": 118, "height": 140, "configurator_id": 2012, "configurator_category": "furniture_item"}, {"name": "Monster Library Shelves", "originX": -301, "originY": 385, "width": 118, "height": 140, "configurator_id": 2013, "configurator_category": "furniture_item"}, {"name": "Scare Can", "originX": -170, "originY": 385, "width": 118, "height": 140, "configurator_id": 2014, "configurator_category": "furniture_item"}, {"name": "Monster Buffet Food", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2015, "configurator_category": "furniture_item"}, {"name": "Monster Library Table", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 2017, "configurator_category": "furniture_item"}, {"name": "Monster Eye Pillar", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 2018, "configurator_category": "furniture_item"}, {"name": "Monster Door Station", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2019, "configurator_category": "furniture_item"}, {"name": "Monster Boombox", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 2023, "configurator_category": "furniture_item"}, {"name": "Monster Ping Pong Table", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 2024, "configurator_category": "furniture_item"}, {"name": "Monster Lounge Chair", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 2025, "configurator_category": "furniture_item"}, {"name": "Imperial Wall Panel", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 2026, "configurator_category": "furniture_item"}, {"name": "Radar Screen", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 2027, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/a84599cf-cf92-4f05-9c09-3931fc3c9fdf_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page47", "layout": {"frames": [{"name": "Droid Cleaning Station", "originX": 62, "originY": 72, "width": 118, "height": 140, "configurator_id": 2028, "configurator_category": "furniture_item"}, {"name": "Holonet Terminal", "originX": 200, "originY": 72, "width": 118, "height": 140, "configurator_id": 2029, "configurator_category": "furniture_item"}, {"name": "Millennium Falcon Seats", "originX": 331, "originY": 72, "width": 118, "height": 140, "configurator_id": 2030, "configurator_category": "furniture_item"}, {"name": "System Readout Terminal", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2031, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON><PERSON>", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 2032, "configurator_category": "furniture_item"}, {"name": "GNK Power Droid", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 2033, "configurator_category": "furniture_item"}, {"name": "Imperial Archway", "originX": 57, "originY": 381, "width": 118, "height": 140, "configurator_id": 2034, "configurator_category": "furniture_item"}, {"name": "Computer Console", "originX": 195, "originY": 382, "width": 118, "height": 140, "configurator_id": 2035, "configurator_category": "furniture_item"}, {"name": "LIN Demolitionmech Droid", "originX": 331, "originY": 383, "width": 118, "height": 140, "configurator_id": 2038, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/b3ea605a-c067-41f5-b69e-9d3c71749f7d_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page48", "layout": {"frames": [{"name": "Droid Cleaning Station", "originX": -435, "originY": 72, "width": 118, "height": 140, "configurator_id": 2028, "configurator_category": "furniture_item"}, {"name": "Holonet Terminal", "originX": -297, "originY": 72, "width": 118, "height": 140, "configurator_id": 2029, "configurator_category": "furniture_item"}, {"name": "Millennium Falcon Seats", "originX": -166, "originY": 72, "width": 118, "height": 140, "configurator_id": 2030, "configurator_category": "furniture_item"}, {"name": "System Readout Terminal", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 2031, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON><PERSON>", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 2032, "configurator_category": "furniture_item"}, {"name": "GNK Power Droid", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 2033, "configurator_category": "furniture_item"}, {"name": "Imperial Archway", "originX": -440, "originY": 381, "width": 118, "height": 140, "configurator_id": 2034, "configurator_category": "furniture_item"}, {"name": "Computer Console", "originX": -302, "originY": 382, "width": 118, "height": 140, "configurator_id": 2035, "configurator_category": "furniture_item"}, {"name": "LIN Demolitionmech Droid", "originX": -166, "originY": 383, "width": 118, "height": 140, "configurator_id": 2038, "configurator_category": "furniture_item"}, {"name": "TIE Fighter Chair", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2039, "configurator_category": "furniture_item"}, {"name": "X-wing Fighter Chair", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 2040, "configurator_category": "furniture_item"}, {"name": "Tatooine House", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 2041, "configurator_category": "furniture_item"}, {"name": "Imperial Throne", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2042, "configurator_category": "furniture_item"}, {"name": "Holonet Tracking Console", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 2043, "configurator_category": "furniture_item"}, {"name": "Radar Computer", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 2044, "configurator_category": "furniture_item"}, {"name": "Swimming Pool", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 2046, "configurator_category": "furniture_item"}, {"name": "Board Wave", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 2047, "configurator_category": "furniture_item"}, {"name": "Mermaid Cutout", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 2048, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/b3ea605a-c067-41f5-b69e-9d3c71749f7d_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page49", "layout": {"frames": [{"name": "<PERSON><PERSON> Cutout", "originX": 62, "originY": 72, "width": 118, "height": 140, "configurator_id": 2049, "configurator_category": "furniture_item"}, {"name": "Lit Stage", "originX": 200, "originY": 72, "width": 118, "height": 140, "configurator_id": 2050, "configurator_category": "furniture_item"}, {"name": "Beach Beatz", "originX": 331, "originY": 72, "width": 118, "height": 140, "configurator_id": 2051, "configurator_category": "furniture_item"}, {"name": "Dressing Table", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2052, "configurator_category": "furniture_item"}, {"name": "Retro Jukebox", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 2053, "configurator_category": "furniture_item"}, {"name": "Inflatable Lounge Chair", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 2054, "configurator_category": "furniture_item"}, {"name": "Dressing Screen", "originX": 57, "originY": 381, "width": 118, "height": 140, "configurator_id": 2055, "configurator_category": "furniture_item"}, {"name": "Lifeguard Chair", "originX": 195, "originY": 382, "width": 118, "height": 140, "configurator_id": 2056, "configurator_category": "furniture_item"}, {"name": "Diving Board", "originX": 331, "originY": 383, "width": 118, "height": 140, "configurator_id": 2058, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/09763c84-155f-45a6-ac1f-25af1b7de54c_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page50", "layout": {"frames": [{"name": "<PERSON><PERSON> Cutout", "originX": -435, "originY": 72, "width": 118, "height": 140, "configurator_id": 2049, "configurator_category": "furniture_item"}, {"name": "Lit Stage", "originX": -297, "originY": 72, "width": 118, "height": 140, "configurator_id": 2050, "configurator_category": "furniture_item"}, {"name": "Beach Beatz", "originX": -166, "originY": 72, "width": 118, "height": 140, "configurator_id": 2051, "configurator_category": "furniture_item"}, {"name": "Dressing Table", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 2052, "configurator_category": "furniture_item"}, {"name": "Retro Jukebox", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 2053, "configurator_category": "furniture_item"}, {"name": "Inflatable Lounge Chair", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 2054, "configurator_category": "furniture_item"}, {"name": "Dressing Screen", "originX": -440, "originY": 381, "width": 118, "height": 140, "configurator_id": 2055, "configurator_category": "furniture_item"}, {"name": "Lifeguard Chair", "originX": -302, "originY": 382, "width": 118, "height": 140, "configurator_id": 2056, "configurator_category": "furniture_item"}, {"name": "Diving Board", "originX": -166, "originY": 383, "width": 118, "height": 140, "configurator_id": 2058, "configurator_category": "furniture_item"}, {"name": "Inner Tube", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2059, "configurator_category": "furniture_item"}, {"name": "<PERSON> <PERSON><PERSON>'s Diner", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 2060, "configurator_category": "furniture_item"}, {"name": "Mic", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 2061, "configurator_category": "furniture_item"}, {"name": "Beach Boards", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2062, "configurator_category": "furniture_item"}, {"name": "Motorbike", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 2063, "configurator_category": "furniture_item"}, {"name": "Gas Pump", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 2064, "configurator_category": "furniture_item"}, {"name": "Castle Gate", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 2065, "configurator_category": "furniture_item"}, {"name": "Stone Keep", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 2066, "configurator_category": "furniture_item"}, {"name": "Watch Tower", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 2067, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/09763c84-155f-45a6-ac1f-25af1b7de54c_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page51", "layout": {"frames": [{"name": "Castle Entrance", "originX": 62, "originY": 72, "width": 118, "height": 140, "configurator_id": 2068, "configurator_category": "furniture_item"}, {"name": "Battlements", "originX": 200, "originY": 72, "width": 118, "height": 140, "configurator_id": 2069, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON><PERSON>", "originX": 331, "originY": 72, "width": 118, "height": 140, "configurator_id": 2071, "configurator_category": "furniture_item"}, {"name": "Ogre Table", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2073, "configurator_category": "furniture_item"}, {"name": "Fairy Tree Stump", "originX": 196, "originY": 231, "width": 118, "height": 140, "configurator_id": 2074, "configurator_category": "furniture_item"}, {"name": "Fairy Flower", "originX": 327, "originY": 231, "width": 118, "height": 140, "configurator_id": 2075, "configurator_category": "furniture_item"}, {"name": "Steel Anvil", "originX": 57, "originY": 381, "width": 118, "height": 140, "configurator_id": 2076, "configurator_category": "furniture_item"}, {"name": "Wish You Well", "originX": 195, "originY": 382, "width": 118, "height": 140, "configurator_id": 2077, "configurator_category": "furniture_item"}, {"name": "Regal Throne", "originX": 331, "originY": 383, "width": 118, "height": 140, "configurator_id": 2078, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/40711f52-e7e4-4c31-80a7-b05a85715a54_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page52", "layout": {"frames": [{"name": "Castle Entrance", "originX": -435, "originY": 72, "width": 118, "height": 140, "configurator_id": 2068, "configurator_category": "furniture_item"}, {"name": "Battlements", "originX": -297, "originY": 72, "width": 118, "height": 140, "configurator_id": 2069, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON><PERSON>", "originX": -166, "originY": 72, "width": 118, "height": 140, "configurator_id": 2071, "configurator_category": "furniture_item"}, {"name": "Ogre Table", "originX": -439, "originY": 231, "width": 118, "height": 140, "configurator_id": 2073, "configurator_category": "furniture_item"}, {"name": "Fairy Tree Stump", "originX": -301, "originY": 231, "width": 118, "height": 140, "configurator_id": 2074, "configurator_category": "furniture_item"}, {"name": "Fairy Flower", "originX": -170, "originY": 231, "width": 118, "height": 140, "configurator_id": 2075, "configurator_category": "furniture_item"}, {"name": "Steel Anvil", "originX": -440, "originY": 381, "width": 118, "height": 140, "configurator_id": 2076, "configurator_category": "furniture_item"}, {"name": "Wish You Well", "originX": -302, "originY": 382, "width": 118, "height": 140, "configurator_id": 2077, "configurator_category": "furniture_item"}, {"name": "Regal Throne", "originX": -166, "originY": 383, "width": 118, "height": 140, "configurator_id": 2078, "configurator_category": "furniture_item"}, {"name": "Potions Table", "originX": 58, "originY": 73, "width": 118, "height": 140, "configurator_id": 2079, "configurator_category": "furniture_item"}, {"name": "Royal Cannon", "originX": 199, "originY": 73, "width": 118, "height": 140, "configurator_id": 2081, "configurator_category": "furniture_item"}, {"name": "Magical Fairy Plant", "originX": 333, "originY": 73, "width": 118, "height": 140, "configurator_id": 2082, "configurator_category": "furniture_item"}, {"name": "Barrel Top", "originX": 58, "originY": 231, "width": 118, "height": 140, "configurator_id": 2083, "configurator_category": "furniture_item"}, {"name": "Ye Olde Puffle Bowl", "originX": 199, "originY": 231, "width": 118, "height": 140, "configurator_id": 2084, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON> <PERSON>", "originX": 333, "originY": 231, "width": 118, "height": 140, "configurator_id": 2085, "configurator_category": "furniture_item"}, {"name": "Stone Gatepost", "originX": 58, "originY": 385, "width": 118, "height": 140, "configurator_id": 2087, "configurator_category": "furniture_item"}, {"name": "<PERSON><PERSON> Cabinet", "originX": 199, "originY": 385, "width": 118, "height": 140, "configurator_id": 2088, "configurator_category": "furniture_item"}, {"name": "Gnarled Tree", "originX": 333, "originY": 385, "width": 118, "height": 140, "configurator_id": 2089, "configurator_category": "furniture_item"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/40711f52-e7e4-4c31-80a7-b05a85715a54_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page53", "layout": {"frames": [{"name": "Snowy Backyard Igloo", "originX": 268, "originY": 106, "width": 118, "height": 140, "configurator_id": 30, "configurator_category": "igloo"}, {"name": "Deluxe Snow Igloo", "originX": 273, "originY": 354, "width": 118, "height": 140, "configurator_id": 9, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/d325f8fa-dc5e-4bd7-b42d-5b3cbb201978_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page54", "layout": {"frames": [{"name": "Snowy Backyard Igloo", "originX": -228, "originY": 106, "width": 118, "height": 140, "configurator_id": 30, "configurator_category": "igloo"}, {"name": "Deluxe Snow Igloo", "originX": -224, "originY": 354, "width": 118, "height": 140, "configurator_id": 9, "configurator_category": "igloo"}, {"name": "Split Level Igloo", "originX": 287, "originY": 106, "width": 118, "height": 140, "configurator_id": 13, "configurator_category": "igloo"}, {"name": "Penthouse", "originX": 292, "originY": 356, "width": 118, "height": 140, "configurator_id": 57, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/d325f8fa-dc5e-4bd7-b42d-5b3cbb201978_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page55", "layout": {"frames": [{"name": "Beach Party Igloo", "originX": 273, "originY": 354, "width": 118, "height": 140, "configurator_id": 63, "configurator_category": "igloo"}, {"name": "Imperial Base Igloo", "originX": 268, "originY": 106, "width": 118, "height": 140, "configurator_id": 62, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/cafa8b2d-58bb-43fd-b0af-2b03dd3940ab_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page56", "layout": {"frames": [{"name": "Beach Party Igloo", "originX": -224, "originY": 354, "width": 118, "height": 140, "configurator_id": 63, "configurator_category": "igloo"}, {"name": "Magical Hideout", "originX": 294, "originY": 106, "width": 118, "height": 140, "configurator_id": 65, "configurator_category": "igloo"}, {"name": "Eerie Castle", "originX": 294, "originY": 356, "width": 118, "height": 140, "configurator_id": 66, "configurator_category": "igloo"}, {"name": "Imperial Base Igloo", "originX": -228, "originY": 106, "width": 118, "height": 140, "configurator_id": 62, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/cafa8b2d-58bb-43fd-b0af-2b03dd3940ab_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page57", "layout": {"frames": [{"name": "Train Station Igloo", "originX": 268, "originY": 106, "width": 118, "height": 140, "configurator_id": 68, "configurator_category": "igloo"}, {"name": "Main Event Igloo", "originX": 273, "originY": 354, "width": 118, "height": 140, "configurator_id": 69, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/ff61b80c-b100-41d7-9153-801f2fa8dd88_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page58", "layout": {"frames": [{"name": "Train Station Igloo", "originX": -228, "originY": 106, "width": 118, "height": 140, "configurator_id": 68, "configurator_category": "igloo"}, {"name": "Main Event Igloo", "originX": -224, "originY": 354, "width": 118, "height": 140, "configurator_id": 69, "configurator_category": "igloo"}, {"name": "CP Airliner", "originX": 294, "originY": 106, "width": 118, "height": 140, "configurator_id": 70, "configurator_category": "igloo"}, {"name": "Puffle Tree House", "originX": 294, "originY": 356, "width": 118, "height": 140, "configurator_id": 71, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/ff61b80c-b100-41d7-9153-801f2fa8dd88_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page59", "layout": {"frames": [{"name": "Secret Base", "originX": 268, "originY": 106, "width": 118, "height": 140, "configurator_id": 61, "configurator_category": "igloo"}, {"name": "Space Dome Igloo", "originX": 273, "originY": 354, "width": 118, "height": 140, "configurator_id": 73, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/1c1f1e09-dfe6-450f-ad83-0797b749ad8a_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page60", "layout": {"frames": [{"name": "Secret Base", "originX": -228, "originY": 106, "width": 118, "height": 140, "configurator_id": 61, "configurator_category": "igloo"}, {"name": "Space Dome Igloo", "originX": -224, "originY": 354, "width": 118, "height": 140, "configurator_id": 73, "configurator_category": "igloo"}, {"name": "Tour Bus Igloo", "originX": 294, "originY": 106, "width": 118, "height": 140, "configurator_id": 75, "configurator_category": "igloo"}, {"name": "Talent Show Stage", "originX": 294, "originY": 356, "width": 118, "height": 140, "configurator_id": 84, "configurator_category": "igloo"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/1c1f1e09-dfe6-450f-ad83-0797b749ad8a_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page61", "layout": {"frames": [{"name": "Beach", "originX": 138, "originY": 258, "width": 159, "height": 253, "configurator_id": 2, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/1b9460a3-8dda-4894-ae41-148152a00d7b_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page62", "layout": {"frames": [{"name": "Beach", "originX": -359, "originY": 258, "width": 159, "height": 253, "configurator_id": 2, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/1b9460a3-8dda-4894-ae41-148152a00d7b_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page63", "layout": {"frames": [{"name": "Forest", "originX": 156, "originY": 251, "width": 172, "height": 262, "configurator_id": 3, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/6ce47fd5-59e7-4076-9d93-994a1d3eb612_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page64", "layout": {"frames": [{"name": "Forest", "originX": -341, "originY": 251, "width": 172, "height": 262, "configurator_id": 3, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/6ce47fd5-59e7-4076-9d93-994a1d3eb612_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page65", "layout": {"frames": [{"name": "Mountain", "originX": 138, "originY": 250, "width": 155, "height": 252, "configurator_id": 4, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/95d023ff-cdf1-432e-bcd6-bfa8e69d134c_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page66", "layout": {"frames": [{"name": "Mountain", "originX": -359, "originY": 250, "width": 155, "height": 252, "configurator_id": 4, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/95d023ff-cdf1-432e-bcd6-bfa8e69d134c_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page67", "layout": {"frames": [{"name": "Distant Planet", "originX": 135, "originY": 245, "width": 163, "height": 262, "configurator_id": 6, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/e50f8fc5-2eb4-41e2-a8fd-9a90644a1cd0_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page68", "layout": {"frames": [{"name": "Distant Planet", "originX": -362, "originY": 245, "width": 163, "height": 262, "configurator_id": 6, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/e50f8fc5-2eb4-41e2-a8fd-9a90644a1cd0_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page69", "layout": {"frames": [{"name": "Soccer Pitch", "originX": 148, "originY": 255, "width": 139, "height": 261, "configurator_id": 7, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/104614b2-4cc5-4a6d-973e-7d64b94d13e6_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page70", "layout": {"frames": [{"name": "Soccer Pitch", "originX": -349, "originY": 255, "width": 139, "height": 261, "configurator_id": 7, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/104614b2-4cc5-4a6d-973e-7d64b94d13e6_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page71", "layout": {"frames": [{"name": "Undersea", "originX": 149, "originY": 267, "width": 135, "height": 249, "configurator_id": 8, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/66d60733-cb6b-40c0-8dc9-826743044a08_left_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_page72", "layout": {"frames": [{"name": "Undersea", "originX": -348, "originY": 267, "width": 135, "height": 249, "configurator_id": 8, "configurator_category": "igloo_location"}], "images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/66d60733-cb6b-40c0-8dc9-826743044a08_right_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}, {"name": "iglooedit_bc", "layout": {"images": [{"name": "page_background", "originX": 15, "originY": 80, "width": 497, "height": 618, "image": "catalog/iglooedit/dc7f69ca-fe3c-4478-8959-8d1236e0680c_bg_617.png"}]}, "originX": 15, "originY": 81, "sourceWidth": 497, "sourceHeight": 617, "width": 497, "height": 617}]}