{"name": "waddle-forever", "version": "1.1.2", "description": "Singleplayer Experience for Club Penguin", "main": "./compiled/client/main.js", "scripts": {"clean": "<PERSON><PERSON><PERSON> compiled/", "clean-client": "rimraf compiled/client/", "copy-files": "copyfiles -u 1 assets/**/* assets/* assets/flash/PepperFlashPlayer.plugin/Contents/* assets/flash/PepperFlashPlayer.plugin/Contents/_CodeSignature/* assets/flash/PepperFlashPlayer.plugin/Contents/MacOs/* compiled/assets/ && copyfiles -u 3 src/client/views/**/* compiled/client/views/", "build-browser": "tsc --project tsconfig.esm.json", "build-tsc": "yarn clean && tsc && yarn build-browser && yarn copy-files", "start": "yarn build-tsc && cross-env NODE_ENV=dev electron ./compiled/client/main.js", "lint": "eslint -c .eslintrc --ext .ts ./src", "build-client-win-nosetup": "yarn build-packages && yarn build-tsc && electron-builder build --dir --win --publish never", "build-client-win": "yarn build-packages && yarn build-tsc && electron-builder build --win --publish never", "build-mac": "yarn build-tsc && electron-builder build --mac --publish never", "build-linux": "yarn build-packages && yarn build-tsc && electron-builder build --linux --x64 --publish never", "dev": "cross-env NODE_ENV=dev nodemon src/server/main.ts", "build-server-win-x64": "tsc&&yarn clean-client&&pkg compiled/server/main.js -o dist/WaddleForeverServer.exe -t node20-win-x64&&node scripts/rename-win.js", "build-server-linux-x64": "tsc&&yarn clean-client&&pkg compiled/server/main.js -o dist/WaddleForeverServer -t node20-linux-x64&&node scripts/rename-linux.js", "build-server-win-arm64": "tsc&&yarn clean-client&&pkg compiled/server/main.js -o dist/WaddleForeverServer-arm64.exe -t node20-win-arm64", "build-server-linux-arm64": "tsc&&yarn clean-client&&pkg compiled/server/main.js -o dist/WaddleForeverServer-arm64 -t node20-linux-arm64", "build-media": "cross-env PRODUCTION_BUILD=PRODUCTION ts-node scripts/zip-media", "build-win": "yarn build-client-win&&node scripts/zip-win", "global-crumbs": "ts-node crumbs/global-crumbs.ts", "news-crumbs": "ts-node crumbs/news-crumbs.ts", "local-crumbs": "ts-node crumbs/local-crumbs.ts", "build-packages": "ts-node scripts/build-packages.ts", "debug-versions": "ts-node scripts/dump-versions.ts"}, "author": "CPS", "license": "MIT", "repository": "https://github.com/nhaar/Waddle-Forever", "build": {"files": ["compiled/**/*", "package.json"], "electronDist": "node_modules/electron/dist", "appId": "com.clubpenguin.speedrunning.client", "productName": "Waddle Forever", "asar": "false", "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}, "win": {"publish": "github", "artifactName": "WaddleForever-Setup-${version}.${ext}"}, "mac": {"category": "game", "icon": "./assets/icon.icns", "artifactName": "WaddleForever-Client-${version}.${ext}"}, "linux": {"category": "game", "publish": "github", "target": ["deb", "AppImage"], "icon": "./assets/icon.icns", "maintainer": "nhaar"}, "appImage": {"synopsis": "WaddleForeverClient", "description": "Waddle Forever's Client", "category": "Games", "artifactName": "WaddleForeverClient-${version}.${ext}"}}, "nodemonConfig": {"ignore": ["/data/*", "settings.json", "info.json"]}, "pkg": {"targets": ["latest-linux-arm64", "latest-win-arm64"], "outputPath": "pkgdist"}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/discord-rpc": "^4.0.3", "@types/electron-prompt": "^1.6.1", "@types/express": "^5.0.0", "@types/node": "^18.13.0", "@types/object-hash": "^3.0.6", "@types/unzipper": "^0.10.10", "@typescript-eslint/eslint-plugin": "^5.52.0", "@typescript-eslint/parser": "^5.52.0", "@yao-pkg/pkg": "^5.15.0", "archiver": "^7.0.1", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "electron": "^10.0.0", "electron-builder": "23.0.2", "eslint": "^8.34.0", "eslint-plugin-prefer-arrow": "^1.2.3", "rimraf": "^4.1.2", "ts-node": "^10.9.2", "typescript": "^4.9.5"}, "dependencies": {"cross-fetch": "^3.1.5", "discord-rpc": "^4.0.1", "electron-fetch": "^1.9.1", "electron-is-dev": "^2.0.0", "electron-log": "^5.0.0-beta.16", "electron-prompt": "^1.7.0", "electron-store": "^8.1.0", "electron-updater": "^5.3.0", "express": "^4.21.0", "object-hash": "^3.0.0", "unzipper": "^0.12.3"}}