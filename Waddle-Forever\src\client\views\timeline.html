<!DOCTYPE html>
<html>
  <head>
    <link href="timeline.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="base.css" rel="stylesheet" type="text/css" media="screen"/>
  </head>
  <body>
    <div class="timeline-header">
      <div class="version-picker">
        <div class="version-description">
          Choose a day in the timeline!
          <br>
          WARNING: This timeline is a
          <br>
          work in progress! It will have errors
        </div>
        <div>
          <div>
            <label for="year">Year:</label>
            <select id="year">
              <option>2005</option>
              <option>2006</option>
              <option>2007</option>
              <option>2008</option>
              <option>2009</option>
              <option>2010</option>
            </select>
            <label for="month">Month:</label>
            <select id="month">
              <option>January</option>
              <option>February</option>
              <option>March</option>
              <option>April</option>
              <option>May</option>
              <option>June</option>
              <option>July</option>
              <option>August</option>
              <option>September</option>
              <option>October</option>
              <option>November</option>
              <option>December</option>
            </select>
          </div>
        </div>
        <div>
          <input type="radio" id="calendar-timeline" name="timeline-type" checked />
          <label for="calendar-timeline">Calendar</label>
          <input type="radio" id="list-timeline" name="timeline-type" />
          <label for="list-timeline">List</label>
        </div>
      </div>
      <div class="version-selected">
        Selected Date: <span id="selected-date"></span>
      </div>
    </div>
    <div id="timeline">
    </div>
    <div id="as3-footer"></div>
    <script src="../static/common-static.js" type="module"></script>
    <script src="../static/timeline-static.js" type="module"></script>
  </body>
</html>