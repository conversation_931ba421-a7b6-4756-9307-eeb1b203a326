/** The day Club Penguin launched its beta */
export const BETA_RELEASE = '2005-08-22';

export const SPORT_SHOP_RELEASE = '2005-11-03';

export const MTN_RELEASE = '2005-11-18';

export const PET_SHOP_RELEASE = '2006-03-17';

export const PIZZA_PARLOR_OPENING_END = '2006-02-28';

export const PIZZA_PARLOR_OPENING_START = '2006-02-24';

export const ICEBERG_RELEASE = '2006-03-29';

export const EARTH_DAY_2010_START = '2010-04-21';

export const EARTH_DAY_2010_END = '2010-04-27';

/** The day the EPF launched */
export const EPF_RELEASE = '2010-05-27';

export const WATER_HUNT_END = '2010-11-24';

export const WATER_CELEBRATION_END = '2010-12-02';

export const PLANET_Y_2010 = '2010-11-19';

export const STADIUM_GAMES_END = '2010-12-20';

export const STAMPS_RELEASE = '2010-07-26';

/** Just a placeholder for skipping from 2010 to 2016 */
export const MODERN_AS3 = '2016-01-01';

export const CAVE_EXPEDITION_END = '2010-01-29';

export const FAIR_2010_START = '2010-09-03';

export const FAIR_2011_START = '2011-09-22';

export const MUSIC_JAM_2010_CONST_START = '2010-07-01';

export const MUSIC_JAM_2010_START = '2010-07-09';

export const JULY_4_2010_END = '2010-07-05';

export const ANNIVERSARY_5_START = '2010-10-23';

export const HALLOWEEN_2010_START = '2010-10-28';

// This got delayed!
export const NEW_YEARS_2010_UPDATE = '2010-01-02';

export const PUFFLE_PARTY_10_CONST_START = '2010-02-11';

export const EGG_HUNT_2006_START = '2006-04-14';

export const EGG_HUNT_2006_END = '2006-04-16';

export const FIRST_BOILER_ROOM_PAPER = '2006-04-13';

export const CAVE_OPENING_START = '2006-05-26';

export const CAVE_OPENING_END = '2006-05-29';

export const SUMMER_PARTY_START = '2006-06-16';

export const SUMMER_PARTY_END = '2006-06-25';

export const LIGHTHOUSE_PARTY_START = '2006-09-21';

export const CHRISTMAS_2006_DECORATION = '2006-12-15';

export const COVE_OPENING_START = '2007-05-25';

export const CHRISTMAS_2007_START = '2007-12-21';

export const WINTER_FIESTA_08_START = '2008-01-18';

export const MUSIC_JAM_08_START = '2008-07-25';

export const DIG_OUT_DOJO_END = '2008-11-14';

export const FIRST_AS3_NEWSPAPER = '2010-11-19';

export const SPORT_PARTY_START = '2006-08-11';

export const CARD_JITSU_RELEASE = '2008-11-17';

export const CHRISTMAS_2005_ENDS = '2005-12-26';

export const FIND_FOUR_RELEASE = '2006-04-27';

export const MISSION_1_RELEASE = '2006-08-18';

export const HQ_REDESIGN = '2006-03-29';

export const SNOW_SPORT_RELEASE = '2007-08-31';

// exact date this client is introduced is unknown
// but the new CPIP system which uses more files than just chat.swf
// it should be in 2007, I put the earliest date in which chat339.swf fails to provide features
// the feature being the lighthouse
export const PRE_CPIP_REWRITE_DATE = LIGHTHOUSE_PARTY_START;

export const FIRST_STAGE_PLAY = '2007-11-16';

// Update is around Oct 23, but this so far is the first practical
// use of AS3 in WF
export const AS3_UPDATE = FIRST_AS3_NEWSPAPER;

/** The day Club Penguin launched the CPIP engine */
export const CPIP_UPDATE = '2008-07-14';

export const ROOM_REDRAWS = '2006-09-22';

export const AQUAGRABBER_RELEASE = '2008-02-19';

export const ROCKHOPPER_ARRIVAL_PARTY_START = '2008-04-25';

export const ROCKHOPPER_ARRIVAL_END = '2008-04-28'

export const MIGRATOR_RECONSTRUCTION_CLEANUP_PHASE = '2008-04-10';

export const SPORT_PARTY_END = '2006-08-21';

export const EARTHQUAKE = '2008-06-20';

export const JPA_RELEASE = '2006-11-06';

export const PLAZA_LAUNCHPAD_START = '2006-10-05';

export const CHAT_339 = EGG_HUNT_2006_END;

export const AGENTCOM_RELEASE = '2008-11-24';

export const GAME_UPGRADES = '2009-03-27';

export const FIRE_CELEBRATION_START = '2009-11-20';

export const FIRE_CONST_START = '2009-11-13';