import RoomScene from "./RoomScene"
import Button from "../components/Button"
import MoveTo from "../components/MoveTo"
import SimpleButton from "../components/SimpleButton"


/* START OF COMPILED CODE */

export default class DojoFire extends RoomScene {

    constructor() {
        super("DojoFire");

        /** @type {Phaser.GameObjects.Image} */
        this.floor;
        /** @type {Array<Phaser.GameObjects.Image|Phaser.GameObjects.Sprite>} */
        this.sort;


        /* START-USER-CTR-CODE */
        this.roomTriggers = {
            'dojo': () => this.triggerRoom(320, 320, 240)
        }
        this.music = 'dojo'
        /* END-USER-CTR-CODE */
    }

    /** @returns {void} */
    _preload() {

        this.load.pack("dojofire-pack", "assets/media/rooms/dojofire/dojofire-pack.json");
    }

    /** @returns {void} */
    _create() {

        // floor
        const floor = this.add.image(760, 480, "dojofire", "bg");
        floor.setOrigin(0.5, 0.5);

        // fire_mat
        const fire_mat = this.add.image(760, 600, "dojofire", "fire_mat");
        fire_mat.setOrigin(0.5, 0.5);

        // sensei_mat
        const sensei_mat = this.add.image(760, 320, "dojofire", "sensei_mat");
        sensei_mat.setOrigin(0.5, 0.5);

        // door
        const door = this.add.image(200, 400, "dojofire", "door");
        door.setOrigin(0.5, 0.5);

        // door_zone
        const door_zone = this.add.rectangle(200, 400, 100, 200);
        door_zone.setOrigin(0.5, 0.5);
        door_zone.isFilled = true;
        door_zone.fillColor = 65280;
        door_zone.fillAlpha = 0.5;

        // card_jitsu_fire_zone
        const card_jitsu_fire_zone = this.add.rectangle(760, 600, 200, 100);
        card_jitsu_fire_zone.setOrigin(0.5, 0.5);
        card_jitsu_fire_zone.isFilled = true;
        card_jitsu_fire_zone.fillColor = 16711680;
        card_jitsu_fire_zone.fillAlpha = 0.5;

        // sensei_zone
        const sensei_zone = this.add.rectangle(760, 320, 150, 100);
        sensei_zone.setOrigin(0.5, 0.5);
        sensei_zone.isFilled = true;
        sensei_zone.fillColor = 255;
        sensei_zone.fillAlpha = 0.5;

        // lists
        const sort = [fire_mat, sensei_mat, door];

        // door_zone (components)
        const door_zoneMoveTo = new MoveTo(door_zone);
        door_zoneMoveTo.x = 320;
        door_zoneMoveTo.y = 320;

        // card_jitsu_fire_zone (components)
        const card_jitsu_fire_zoneSimpleButton = new SimpleButton(card_jitsu_fire_zone);
        card_jitsu_fire_zoneSimpleButton.callback = () => this.onCardJitsuFire();

        // sensei_zone (components)
        const sensei_zoneSimpleButton = new SimpleButton(sensei_zone);
        sensei_zoneSimpleButton.callback = () => this.onSensei();

        this.floor = floor;
        this.sort = sort;

        this.events.emit("scene-awake");
    }


    /* START-USER-CODE */

    onCardJitsuFire() {
        this.interface.loadWidget('CardJitsuFire')
    }

    onSensei() {
        // Show sensei dialog for fire dojo
        this.interface.prompt.showWindow('Talk to Sensei about the Fire element?', 'dual', () => {
            this.interface.loadWidget('CardJitsuFire')
        })
    }

    /* END-USER-CODE */
}

/* END OF COMPILED CODE */
