#!/usr/bin/env node

/**
 * Database Migration Script
 * Adds email verification fields to existing users table
 */

const mysql = require('mysql2/promise')
const fs = require('fs')
const path = require('path')

async function migrate() {
    console.log('🔄 Migrating database for email functionality...')

    try {
        // Load config
        const configPath = path.join(__dirname, 'config', 'config.json')
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))

        // Connect to database
        const connection = await mysql.createConnection({
            host: config.database.host,
            user: config.database.user,
            password: config.database.password,
            database: config.database.database
        })

        console.log('✅ Connected to database')

        // Check if columns already exist
        const [columns] = await connection.execute(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users'
        `, [config.database.database])

        const existingColumns = columns.map(row => row.COLUMN_NAME)

        // Add email verification columns if they don't exist
        const newColumns = [
            {
                name: 'emailVerified',
                sql: 'ALTER TABLE users ADD COLUMN emailVerified TINYINT(1) NOT NULL DEFAULT 0'
            },
            {
                name: 'emailVerificationToken',
                sql: 'ALTER TABLE users ADD COLUMN emailVerificationToken VARCHAR(64) DEFAULT NULL'
            },
            {
                name: 'emailVerificationExpiry',
                sql: 'ALTER TABLE users ADD COLUMN emailVerificationExpiry TIMESTAMP NULL DEFAULT NULL'
            },
            {
                name: 'passwordResetToken',
                sql: 'ALTER TABLE users ADD COLUMN passwordResetToken VARCHAR(64) DEFAULT NULL'
            },
            {
                name: 'passwordResetExpiry',
                sql: 'ALTER TABLE users ADD COLUMN passwordResetExpiry TIMESTAMP NULL DEFAULT NULL'
            }
        ]

        for (const column of newColumns) {
            if (!existingColumns.includes(column.name)) {
                console.log(`➕ Adding column: ${column.name}`)
                await connection.execute(column.sql)
            } else {
                console.log(`✅ Column already exists: ${column.name}`)
            }
        }

        // Set existing users as email verified (so they can still log in)
        const [result] = await connection.execute(`
            UPDATE users 
            SET emailVerified = 1 
            WHERE emailVerified = 0 AND email IS NOT NULL
        `)

        if (result.affectedRows > 0) {
            console.log(`✅ Marked ${result.affectedRows} existing users as email verified`)
        }

        await connection.end()
        console.log('🎉 Database migration completed successfully!')

    } catch (error) {
        console.error('❌ Migration failed:', error.message)
        process.exit(1)
    }
}

migrate()
