<!DOCTYPE html>
<html>
  <head>
    <link href="settings.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="base.css" rel="stylesheet" type="text/css" media="screen"/>
  </head>
  <body>
    <div>
      Settings
    </div>
    <div>
      The game will automatically clear cache or reload the game if needed.
    </div>
    <div class="settings-parent">
      <div class="settings-header">
        Additional Packages
      </div>
      <div class="settings">
        <div>Clothing Package</div>
        <input type="checkbox" class="js-clothing-input" />
      </div>
      <div class="settings-header">
        Speedrun Related
      </div>
      <div class="settings">
        <div>30 FPS</div>
        <input type="checkbox" class="js-fps-input" />
        <div>
          Thin Ice IGT
        </div>
        <input type="checkbox" class="js-thin-ice-igt-input" />
        <div>
          Jet Pack Adventure Level Selector
        </div>
        <input type="checkbox" class="js-jpa-level-input" />
      </div>
      <div class="settings-header">
        Quality of Life
      </div>
      <div class="settings">
        <div>
          Swap Dance Contest Arrow Keys
        </div>
        <input type="checkbox" class="js-dance-arrow-input" />
        <div>Remove Puffle Quest Waiting</div>
        <input type="checkbox" class="rainbow-input" />
      </div>
      <div class="settings-header">
        Extra
      </div>
      <div class="settings">
        <div>Simpler Website</div>
        <input type="checkbox" class="js-website-input" />
      </div>
      <div class="settings">
        <div>New Accounts Are Always a Member</div>
        <input type="checkbox" class="js-member-input" />
      </div>
      <div class="settings">
        <div>2013 My Puffle</div>
        <input type="checkbox" class="js-my-puffle-input" />
      </div>
    </div>
    <script>var exports = {};</script>
    <script src="../static/common-static.js" type="module"></script>
    <script src="../static/settings-static.js" type="module"></script>
  </body>
</html>