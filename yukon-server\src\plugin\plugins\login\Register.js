import GamePlugin from '@plugin/GamePlugin'
import bcrypt from 'bcrypt'
import crypto from 'crypto'
import { hasProps, isLength, isString, isEmail } from '@utils/validation'

export default class Register extends GamePlugin {

    constructor(handler) {
        super(handler)

        this.events = {
            'register': this.register,
            'verify_email': this.verifyEmail,
            'resend_verification': this.resendVerification,
            'forgot_password': this.forgotPassword,
            'reset_password': this.resetPassword
        }

        this.responses = {
            success: { success: true, message: 'Registration successful' },
            usernameTaken: { success: false, message: 'Username already taken' },
            emailTaken: { success: false, message: 'Email already registered' },
            invalidData: { success: false, message: 'Invalid registration data' },
            registrationFailed: { success: false, message: 'Registration failed' },
            emailNotVerified: { success: false, message: 'Please verify your email before logging in' },
            verificationSent: { success: true, message: 'Verification email sent' },
            verificationFailed: { success: false, message: 'Email verification failed' },
            passwordResetSent: { success: true, message: 'Password reset email sent' },
            passwordResetFailed: { success: false, message: 'Password reset failed' },
            userNotFound: { success: false, message: 'User not found' }
        }
    }

    async register(args, user) {
        if (user.loginSent) {
            return user.close()
        }

        user.loginSent = true

        // Validate input
        let validation = this.validateRegistration(args)
        if (validation !== true) {
            user.send('register', validation)
            return user.close()
        }

        try {
            // Check if username exists
            const existingUser = await this.db.users.findOne({
                where: { username: args.username }
            })

            if (existingUser) {
                user.send('register', this.responses.usernameTaken)
                return user.close()
            }

            // Check if email exists
            const existingEmail = await this.db.users.findOne({
                where: { email: args.email }
            })

            if (existingEmail) {
                user.send('register', this.responses.emailTaken)
                return user.close()
            }

            // Hash password
            const hashedPassword = await bcrypt.hash(args.password, this.config.crypto.rounds)
            const phpCompatibleHash = hashedPassword.replace('$2b$', '$2a$')

            // Create user
            const newUser = await this.db.users.create({
                username: args.username,
                email: args.email,
                password: phpCompatibleHash,
                emailVerified: this.config.email?.verification?.enabled ? 0 : 1
            })

            // Send verification email if enabled
            if (this.config.email?.verification?.enabled && this.handler.emailService) {
                const baseUrl = args.baseUrl || 'http://localhost:8080'
                await this.handler.emailService.sendVerificationEmail(newUser, baseUrl)
            }

            user.send('register', this.responses.success)

        } catch (error) {
            console.error('Registration error:', error)
            user.send('register', this.responses.registrationFailed)
        }

        user.close()
    }

    async verifyEmail(args, user) {
        if (!hasProps(args, 'userId', 'token')) {
            return user.send('verify_email', this.responses.invalidData)
        }

        if (!this.handler.emailService) {
            return user.send('verify_email', this.responses.verificationFailed)
        }

        const result = await this.handler.emailService.verifyEmail(args.userId, args.token)
        user.send('verify_email', result)
    }

    async resendVerification(args, user) {
        if (!hasProps(args, 'email')) {
            return user.send('resend_verification', this.responses.invalidData)
        }

        try {
            const existingUser = await this.db.users.findOne({
                where: { email: args.email }
            })

            if (!existingUser) {
                return user.send('resend_verification', this.responses.userNotFound)
            }

            if (existingUser.emailVerified) {
                return user.send('resend_verification', { 
                    success: false, 
                    message: 'Email already verified' 
                })
            }

            if (this.handler.emailService) {
                const baseUrl = args.baseUrl || 'http://localhost:8080'
                await this.handler.emailService.sendVerificationEmail(existingUser, baseUrl)
            }

            user.send('resend_verification', this.responses.verificationSent)

        } catch (error) {
            console.error('Resend verification error:', error)
            user.send('resend_verification', this.responses.verificationFailed)
        }
    }

    async forgotPassword(args, user) {
        if (!hasProps(args, 'email')) {
            return user.send('forgot_password', this.responses.invalidData)
        }

        try {
            const existingUser = await this.db.users.findOne({
                where: { email: args.email }
            })

            if (!existingUser) {
                // Don't reveal if email exists for security
                return user.send('forgot_password', this.responses.passwordResetSent)
            }

            if (this.handler.emailService) {
                const baseUrl = args.baseUrl || 'http://localhost:8080'
                await this.handler.emailService.sendPasswordResetEmail(existingUser, baseUrl)
            }

            user.send('forgot_password', this.responses.passwordResetSent)

        } catch (error) {
            console.error('Forgot password error:', error)
            user.send('forgot_password', this.responses.passwordResetFailed)
        }
    }

    async resetPassword(args, user) {
        if (!hasProps(args, 'userId', 'token', 'newPassword')) {
            return user.send('reset_password', this.responses.invalidData)
        }

        if (!isLength(args.newPassword, 6, 60)) {
            return user.send('reset_password', { 
                success: false, 
                message: 'Password must be 6-60 characters' 
            })
        }

        try {
            if (!this.handler.emailService) {
                return user.send('reset_password', this.responses.passwordResetFailed)
            }

            // Validate token
            const validation = await this.handler.emailService.validatePasswordResetToken(
                args.userId, 
                args.token
            )

            if (!validation.success) {
                return user.send('reset_password', validation)
            }

            // Hash new password
            const hashedPassword = await bcrypt.hash(args.newPassword, this.config.crypto.rounds)
            const phpCompatibleHash = hashedPassword.replace('$2b$', '$2a$')

            // Update password and clear reset token
            await this.db.users.update({
                password: phpCompatibleHash,
                passwordResetToken: null,
                passwordResetExpiry: null
            }, {
                where: { id: args.userId }
            })

            user.send('reset_password', { 
                success: true, 
                message: 'Password reset successfully' 
            })

        } catch (error) {
            console.error('Reset password error:', error)
            user.send('reset_password', this.responses.passwordResetFailed)
        }
    }

    validateRegistration(args) {
        if (!hasProps(args, 'username', 'email', 'password')) {
            return this.responses.invalidData
        }

        if (!isLength(args.username, 4, 12) || !isString(args.username)) {
            return { success: false, message: 'Username must be 4-12 characters' }
        }

        if (!isEmail(args.email)) {
            return { success: false, message: 'Invalid email address' }
        }

        if (!isLength(args.password, 6, 60) || !isString(args.password)) {
            return { success: false, message: 'Password must be 6-60 characters' }
        }

        // Check for valid username characters
        if (!/^[a-zA-Z0-9_]+$/.test(args.username)) {
            return { success: false, message: 'Username can only contain letters, numbers, and underscores' }
        }

        return true
    }
}
